/**
 * LightingEditor Component Unit Tests
 * 
 * Comprehensive tests for the LightingEditor component
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import LightingEditor from '../../../directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/LightingEditor.vue';

// Mock API client
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn()
};

// Mock data
const mockLightingSetup = {
  id: 'lighting-1',
  showroom_id: 'showroom-1',
  vendor_id: 'vendor-1',
  name: 'Default Lighting',
  lights: [
    {
      id: 'light-1',
      type: 'directional',
      name: 'Sun Light',
      position: { x: 0, y: 10, z: 5 },
      rotation: { x: -45, y: 0, z: 0 },
      color: '#FFFFFF',
      intensity: 1.0,
      shadows: true,
      enabled: true
    },
    {
      id: 'light-2',
      type: 'point',
      name: 'Accent Light',
      position: { x: 2, y: 3, z: 1 },
      color: '#FFDDAA',
      intensity: 0.8,
      range: 10,
      shadows: false,
      enabled: true
    }
  ],
  environment: {
    skybox: '/environments/studio.hdr',
    ambient_intensity: 0.3,
    ambient_color: '#404040',
    fog_enabled: false,
    fog_color: '#CCCCCC',
    fog_density: 0.01
  }
};

const mockLightTypes = [
  { id: 'directional', name: 'Directional Light', icon: 'wb_sunny' },
  { id: 'point', name: 'Point Light', icon: 'lightbulb' },
  { id: 'spot', name: 'Spot Light', icon: 'flashlight_on' },
  { id: 'area', name: 'Area Light', icon: 'crop_landscape' }
];

const mockEnvironments = [
  { id: 'studio', name: 'Studio', file: '/environments/studio.hdr' },
  { id: 'outdoor', name: 'Outdoor', file: '/environments/outdoor.hdr' },
  { id: 'interior', name: 'Interior', file: '/environments/interior.hdr' }
];

describe('LightingEditor Component', () => {
  let wrapper: VueWrapper<any>;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup default API responses
    mockApi.get.mockImplementation((url: string) => {
      if (url.includes('/items/showroom_lighting/')) {
        return Promise.resolve({ data: { data: mockLightingSetup } });
      }
      if (url.includes('/items/showroom_lighting')) {
        return Promise.resolve({ data: { data: [mockLightingSetup] } });
      }
      return Promise.resolve({ data: { data: [] } });
    });

    mockApi.post.mockResolvedValue({ data: { data: { id: 'new-lighting-id' } } });
    mockApi.patch.mockResolvedValue({ data: { data: mockLightingSetup } });

    // Mount component
    wrapper = mount(LightingEditor, {
      props: {
        vendorId: 'vendor-1',
        showroomId: 'showroom-1'
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('Component Rendering', () => {
    it('should render the component', () => {
      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('.lighting-editor').exists()).toBe(true);
    });

    it('should render the header with title and actions', () => {
      expect(wrapper.find('.editor-title').text()).toBe('Lighting Editor');
      expect(wrapper.find('.editor-subtitle').text()).toBe('Configure lighting and environment');
      
      const saveButton = wrapper.find('button:contains("Save")');
      const resetButton = wrapper.find('button:contains("Reset")');
      
      expect(saveButton.exists()).toBe(true);
      expect(resetButton.exists()).toBe(true);
    });

    it('should render the lights panel', () => {
      expect(wrapper.find('.lights-panel').exists()).toBe(true);
      expect(wrapper.find('.add-light-button').exists()).toBe(true);
    });

    it('should render the environment panel', () => {
      expect(wrapper.find('.environment-panel').exists()).toBe(true);
      expect(wrapper.find('.skybox-selector').exists()).toBe(true);
    });

    it('should render the lighting preview', () => {
      expect(wrapper.find('.lighting-preview').exists()).toBe(true);
      expect(wrapper.find('.preview-scene').exists()).toBe(true);
    });
  });

  describe('Props Validation', () => {
    it('should require vendorId prop', () => {
      expect(LightingEditor.props.vendorId.required).toBe(true);
      expect(LightingEditor.props.vendorId.type).toBe(String);
    });

    it('should have optional showroomId prop', () => {
      expect(LightingEditor.props.showroomId.required).toBeFalsy();
      expect(LightingEditor.props.showroomId.type).toBe(String);
      expect(LightingEditor.props.showroomId.default).toBe(null);
    });
  });

  describe('Data Loading', () => {
    it('should load lighting setup when showroomId is provided', async () => {
      await wrapper.vm.$nextTick();
      
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/showroom_lighting')
      );
    });

    it('should initialize with default lighting when no showroomId', async () => {
      const newWrapper = mount(LightingEditor, {
        props: {
          vendorId: 'vendor-1'
        },
        global: {
          mocks: {
            $api: mockApi
          }
        }
      });

      expect(newWrapper.vm.lightingSetup.name).toBe('New Lighting Setup');
      expect(newWrapper.vm.lightingSetup.lights).toEqual([]);
      
      newWrapper.unmount();
    });
  });

  describe('Light Management', () => {
    it('should add new light', async () => {
      const initialLightCount = wrapper.vm.lightingSetup.lights.length;
      
      await wrapper.vm.addLight('point');
      
      expect(wrapper.vm.lightingSetup.lights).toHaveLength(initialLightCount + 1);
      
      const newLight = wrapper.vm.lightingSetup.lights[initialLightCount];
      expect(newLight.type).toBe('point');
      expect(newLight.name).toBe('New Point Light');
      expect(newLight.enabled).toBe(true);
    });

    it('should remove light', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const initialLightCount = wrapper.vm.lightingSetup.lights.length;
      
      await wrapper.vm.removeLight(0);
      
      expect(wrapper.vm.lightingSetup.lights).toHaveLength(initialLightCount - 1);
    });

    it('should duplicate light', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const originalLight = wrapper.vm.lightingSetup.lights[0];
      const initialLightCount = wrapper.vm.lightingSetup.lights.length;
      
      await wrapper.vm.duplicateLight(0);
      
      expect(wrapper.vm.lightingSetup.lights).toHaveLength(initialLightCount + 1);
      
      const duplicatedLight = wrapper.vm.lightingSetup.lights[initialLightCount];
      expect(duplicatedLight.type).toBe(originalLight.type);
      expect(duplicatedLight.name).toBe(`${originalLight.name} Copy`);
    });

    it('should select light', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const light = wrapper.vm.lightingSetup.lights[0];
      
      await wrapper.vm.selectLight(light);
      
      expect(wrapper.vm.selectedLight).toEqual(light);
    });

    it('should toggle light enabled state', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const light = wrapper.vm.lightingSetup.lights[0];
      const initialEnabled = light.enabled;
      
      await wrapper.vm.toggleLight(light);
      
      expect(light.enabled).toBe(!initialEnabled);
    });
  });

  describe('Light Properties', () => {
    it('should update light position', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const light = wrapper.vm.lightingSetup.lights[0];
      
      await wrapper.vm.updateLightPosition(light, { x: 5, y: 8, z: 3 });
      
      expect(light.position).toEqual({ x: 5, y: 8, z: 3 });
    });

    it('should update light rotation', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const light = wrapper.vm.lightingSetup.lights[0];
      
      await wrapper.vm.updateLightRotation(light, { x: -30, y: 45, z: 0 });
      
      expect(light.rotation).toEqual({ x: -30, y: 45, z: 0 });
    });

    it('should update light color', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const light = wrapper.vm.lightingSetup.lights[0];
      
      await wrapper.vm.updateLightColor(light, '#FF5500');
      
      expect(light.color).toBe('#FF5500');
    });

    it('should update light intensity', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const light = wrapper.vm.lightingSetup.lights[0];
      
      await wrapper.vm.updateLightIntensity(light, 1.5);
      
      expect(light.intensity).toBe(1.5);
    });

    it('should update light range for point lights', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const pointLight = wrapper.vm.lightingSetup.lights.find(l => l.type === 'point');
      
      await wrapper.vm.updateLightRange(pointLight, 15);
      
      expect(pointLight.range).toBe(15);
    });

    it('should update spot light cone angle', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      // Add a spot light for testing
      const spotLight = {
        id: 'spot-1',
        type: 'spot',
        name: 'Spot Light',
        position: { x: 0, y: 5, z: 0 },
        rotation: { x: -90, y: 0, z: 0 },
        color: '#FFFFFF',
        intensity: 1.0,
        range: 10,
        cone_angle: 30,
        penumbra: 0.1,
        enabled: true
      };
      wrapper.vm.lightingSetup.lights.push(spotLight);
      
      await wrapper.vm.updateSpotLightCone(spotLight, 45, 0.2);
      
      expect(spotLight.cone_angle).toBe(45);
      expect(spotLight.penumbra).toBe(0.2);
    });

    it('should toggle light shadows', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const light = wrapper.vm.lightingSetup.lights[0];
      const initialShadows = light.shadows;
      
      await wrapper.vm.toggleLightShadows(light);
      
      expect(light.shadows).toBe(!initialShadows);
    });
  });

  describe('Environment Settings', () => {
    it('should update skybox', async () => {
      await wrapper.vm.updateSkybox('/environments/new_skybox.hdr');
      
      expect(wrapper.vm.lightingSetup.environment.skybox).toBe('/environments/new_skybox.hdr');
    });

    it('should update ambient lighting', async () => {
      await wrapper.vm.updateAmbientLighting(0.5, '#606060');
      
      expect(wrapper.vm.lightingSetup.environment.ambient_intensity).toBe(0.5);
      expect(wrapper.vm.lightingSetup.environment.ambient_color).toBe('#606060');
    });

    it('should toggle fog', async () => {
      const initialFogEnabled = wrapper.vm.lightingSetup.environment.fog_enabled;
      
      await wrapper.vm.toggleFog();
      
      expect(wrapper.vm.lightingSetup.environment.fog_enabled).toBe(!initialFogEnabled);
    });

    it('should update fog settings', async () => {
      await wrapper.vm.updateFogSettings('#AAAAAA', 0.02);
      
      expect(wrapper.vm.lightingSetup.environment.fog_color).toBe('#AAAAAA');
      expect(wrapper.vm.lightingSetup.environment.fog_density).toBe(0.02);
    });
  });

  describe('Lighting Presets', () => {
    it('should apply lighting preset', async () => {
      const preset = {
        name: 'Studio Lighting',
        lights: [
          {
            type: 'directional',
            name: 'Key Light',
            position: { x: 2, y: 8, z: 4 },
            rotation: { x: -60, y: 30, z: 0 },
            color: '#FFFFFF',
            intensity: 1.2,
            shadows: true,
            enabled: true
          }
        ],
        environment: {
          skybox: '/environments/studio.hdr',
          ambient_intensity: 0.2,
          ambient_color: '#303030'
        }
      };
      
      await wrapper.vm.applyPreset(preset);
      
      expect(wrapper.vm.lightingSetup.lights).toHaveLength(1);
      expect(wrapper.vm.lightingSetup.lights[0].name).toBe('Key Light');
      expect(wrapper.vm.lightingSetup.environment.ambient_intensity).toBe(0.2);
    });

    it('should save current setup as preset', async () => {
      wrapper.vm.lightingSetup = { ...mockLightingSetup };
      
      await wrapper.vm.saveAsPreset('Custom Lighting');
      
      expect(mockApi.post).toHaveBeenCalledWith(
        '/items/lighting_presets',
        expect.objectContaining({
          name: 'Custom Lighting',
          vendor_id: 'vendor-1'
        })
      );
    });
  });

  describe('Light Gizmos and Interaction', () => {
    it('should start light dragging', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const light = wrapper.vm.lightingSetup.lights[0];
      
      await wrapper.vm.startLightDrag(light, { x: 100, y: 200 });
      
      expect(wrapper.vm.draggedLight).toEqual(light);
      expect(wrapper.vm.dragStartPosition).toEqual({ x: 100, y: 200 });
    });

    it('should update light position during drag', async () => {
      wrapper.vm.lightingSetup.lights = [...mockLightingSetup.lights];
      const light = wrapper.vm.lightingSetup.lights[0];
      wrapper.vm.draggedLight = light;
      wrapper.vm.dragStartPosition = { x: 100, y: 200 };
      
      await wrapper.vm.updateLightDrag({ x: 150, y: 250 });
      
      // Position should be updated based on drag delta
      expect(light.position.x).not.toBe(mockLightingSetup.lights[0].position.x);
    });

    it('should end light dragging', async () => {
      wrapper.vm.draggedLight = wrapper.vm.lightingSetup.lights[0];
      
      await wrapper.vm.endLightDrag();
      
      expect(wrapper.vm.draggedLight).toBe(null);
      expect(wrapper.vm.dragStartPosition).toBe(null);
    });
  });

  describe('Save and Reset Functionality', () => {
    it('should save lighting setup when save button is clicked', async () => {
      wrapper.vm.lightingSetup = { ...mockLightingSetup, name: 'Updated Lighting' };
      
      await wrapper.vm.saveLightingSetup();
      
      expect(mockApi.patch).toHaveBeenCalledWith(
        '/items/showroom_lighting/lighting-1',
        expect.objectContaining({
          name: 'Updated Lighting'
        })
      );
    });

    it('should create new lighting setup when no ID exists', async () => {
      wrapper.vm.lightingSetup = { ...mockLightingSetup, id: null };
      
      await wrapper.vm.saveLightingSetup();
      
      expect(mockApi.post).toHaveBeenCalledWith(
        '/items/showroom_lighting',
        expect.objectContaining({
          vendor_id: 'vendor-1',
          showroom_id: 'showroom-1'
        })
      );
    });

    it('should reset lighting setup to original state', async () => {
      wrapper.vm.originalLightingSetup = { ...mockLightingSetup };
      wrapper.vm.lightingSetup = { ...mockLightingSetup, name: 'Changed Name' };
      
      await wrapper.vm.resetLightingSetup();
      
      expect(wrapper.vm.lightingSetup.name).toBe(mockLightingSetup.name);
      expect(wrapper.vm.hasChanges).toBe(false);
    });

    it('should validate lighting setup before saving', async () => {
      wrapper.vm.lightingSetup = { ...mockLightingSetup, name: '' };
      
      await wrapper.vm.saveLightingSetup();
      
      // Should not call API with invalid data
      expect(mockApi.patch).not.toHaveBeenCalled();
      expect(mockApi.post).not.toHaveBeenCalled();
    });
  });

  describe('Preview Controls', () => {
    it('should update preview camera position', async () => {
      await wrapper.vm.updatePreviewCamera({ x: 5, y: 3, z: 8 });
      
      expect(wrapper.vm.previewCamera.position).toEqual({ x: 5, y: 3, z: 8 });
    });

    it('should change preview object', async () => {
      await wrapper.vm.setPreviewObject('sphere');
      
      expect(wrapper.vm.previewObject).toBe('sphere');
    });

    it('should toggle real-time preview', async () => {
      const initialRealTime = wrapper.vm.realTimePreview;
      
      await wrapper.vm.toggleRealTimePreview();
      
      expect(wrapper.vm.realTimePreview).toBe(!initialRealTime);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors during save', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockApi.patch.mockRejectedValueOnce(new Error('Save failed'));
      
      await wrapper.vm.saveLightingSetup();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should handle API errors during load', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockApi.get.mockRejectedValueOnce(new Error('Load failed'));
      
      await wrapper.vm.loadLightingSetup();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });
});
