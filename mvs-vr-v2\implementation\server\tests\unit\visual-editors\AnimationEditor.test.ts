/**
 * AnimationEditor Component Unit Tests
 * 
 * Comprehensive tests for the AnimationEditor component
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import AnimationEditor from '../../../directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/AnimationEditor.vue';

// Mock API client
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn()
};

// Mock data
const mockAnimation = {
  id: 'animation-1',
  name: 'Door Opening',
  vendor_id: 'vendor-1',
  target_object: 'door-1',
  duration: 2.0,
  loop: false,
  auto_play: false,
  keyframes: [
    {
      id: 'keyframe-1',
      time: 0.0,
      properties: {
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      },
      easing: 'linear'
    },
    {
      id: 'keyframe-2',
      time: 2.0,
      properties: {
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 90, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      },
      easing: 'ease-out'
    }
  ],
  triggers: [
    {
      id: 'trigger-1',
      type: 'click',
      target: 'door-1',
      action: 'play',
      delay: 0
    }
  ]
};

const mockAnimations = [mockAnimation];

const mockEasingTypes = [
  { id: 'linear', name: 'Linear' },
  { id: 'ease-in', name: 'Ease In' },
  { id: 'ease-out', name: 'Ease Out' },
  { id: 'ease-in-out', name: 'Ease In Out' },
  { id: 'bounce', name: 'Bounce' },
  { id: 'elastic', name: 'Elastic' }
];

const mockTriggerTypes = [
  { id: 'click', name: 'Click' },
  { id: 'hover', name: 'Hover' },
  { id: 'proximity', name: 'Proximity' },
  { id: 'timer', name: 'Timer' },
  { id: 'sequence', name: 'Sequence' }
];

describe('AnimationEditor Component', () => {
  let wrapper: VueWrapper<any>;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup default API responses
    mockApi.get.mockImplementation((url: string) => {
      if (url.includes('/items/animations/')) {
        return Promise.resolve({ data: { data: mockAnimation } });
      }
      if (url.includes('/items/animations')) {
        return Promise.resolve({ data: { data: mockAnimations } });
      }
      return Promise.resolve({ data: { data: [] } });
    });

    mockApi.post.mockResolvedValue({ data: { data: { id: 'new-animation-id' } } });
    mockApi.patch.mockResolvedValue({ data: { data: mockAnimation } });

    // Mount component
    wrapper = mount(AnimationEditor, {
      props: {
        vendorId: 'vendor-1',
        animationId: 'animation-1'
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('Component Rendering', () => {
    it('should render the component', () => {
      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('.animation-editor').exists()).toBe(true);
    });

    it('should render the header with title and actions', () => {
      expect(wrapper.find('.editor-title').text()).toBe('Animation Editor');
      expect(wrapper.find('.editor-subtitle').text()).toBe('Create and edit animations');
      
      const saveButton = wrapper.find('button:contains("Save")');
      const resetButton = wrapper.find('button:contains("Reset")');
      
      expect(saveButton.exists()).toBe(true);
      expect(resetButton.exists()).toBe(true);
    });

    it('should render the animation library', () => {
      expect(wrapper.find('.animation-library').exists()).toBe(true);
      expect(wrapper.find('.search-box').exists()).toBe(true);
    });

    it('should render the timeline', () => {
      expect(wrapper.find('.timeline').exists()).toBe(true);
      expect(wrapper.find('.timeline-track').exists()).toBe(true);
    });

    it('should render the properties panel', () => {
      expect(wrapper.find('.properties-panel').exists()).toBe(true);
      expect(wrapper.find('.animation-settings').exists()).toBe(true);
    });

    it('should render the preview panel', () => {
      expect(wrapper.find('.animation-preview').exists()).toBe(true);
      expect(wrapper.find('.playback-controls').exists()).toBe(true);
    });
  });

  describe('Props Validation', () => {
    it('should require vendorId prop', () => {
      expect(AnimationEditor.props.vendorId.required).toBe(true);
      expect(AnimationEditor.props.vendorId.type).toBe(String);
    });

    it('should have optional animationId prop', () => {
      expect(AnimationEditor.props.animationId.required).toBeFalsy();
      expect(AnimationEditor.props.animationId.type).toBe(String);
      expect(AnimationEditor.props.animationId.default).toBe(null);
    });
  });

  describe('Data Loading', () => {
    it('should load animations on mount', async () => {
      await wrapper.vm.$nextTick();
      
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/animations')
      );
    });

    it('should load specific animation when animationId is provided', async () => {
      await wrapper.vm.$nextTick();
      
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/animations/animation-1')
      );
    });

    it('should initialize with default animation when no animationId', async () => {
      const newWrapper = mount(AnimationEditor, {
        props: {
          vendorId: 'vendor-1'
        },
        global: {
          mocks: {
            $api: mockApi
          }
        }
      });

      expect(newWrapper.vm.animation.name).toBe('New Animation');
      expect(newWrapper.vm.animation.duration).toBe(1.0);
      expect(newWrapper.vm.animation.keyframes).toEqual([]);
      
      newWrapper.unmount();
    });
  });

  describe('Animation Selection and Filtering', () => {
    it('should filter animations by search query', async () => {
      wrapper.vm.animations = mockAnimations;
      wrapper.vm.searchQuery = 'Door';
      
      await wrapper.vm.filterAnimations();
      
      expect(wrapper.vm.filteredAnimations).toHaveLength(1);
      expect(wrapper.vm.filteredAnimations[0].name).toBe('Door Opening');
    });

    it('should select animation when clicked', async () => {
      const animation = mockAnimations[0];
      
      await wrapper.vm.selectAnimation(animation);
      
      expect(wrapper.vm.selectedAnimation).toEqual(animation);
      expect(wrapper.vm.animation).toEqual(animation);
    });
  });

  describe('Animation Properties', () => {
    it('should update animation name', async () => {
      const nameInput = wrapper.find('input[placeholder="Animation name"]');
      await nameInput.setValue('Updated Animation Name');
      
      expect(wrapper.vm.animation.name).toBe('Updated Animation Name');
    });

    it('should update animation duration', async () => {
      await wrapper.vm.updateDuration(3.5);
      
      expect(wrapper.vm.animation.duration).toBe(3.5);
    });

    it('should toggle loop setting', async () => {
      const initialLoop = wrapper.vm.animation.loop;
      
      await wrapper.vm.toggleLoop();
      
      expect(wrapper.vm.animation.loop).toBe(!initialLoop);
    });

    it('should toggle auto-play setting', async () => {
      const initialAutoPlay = wrapper.vm.animation.auto_play;
      
      await wrapper.vm.toggleAutoPlay();
      
      expect(wrapper.vm.animation.auto_play).toBe(!initialAutoPlay);
    });

    it('should update target object', async () => {
      await wrapper.vm.updateTargetObject('new-target');
      
      expect(wrapper.vm.animation.target_object).toBe('new-target');
    });
  });

  describe('Keyframe Management', () => {
    it('should add new keyframe', async () => {
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      const initialKeyframeCount = wrapper.vm.animation.keyframes.length;
      
      await wrapper.vm.addKeyframe(1.0);
      
      expect(wrapper.vm.animation.keyframes).toHaveLength(initialKeyframeCount + 1);
      
      const newKeyframe = wrapper.vm.animation.keyframes.find(k => k.time === 1.0);
      expect(newKeyframe).toBeDefined();
      expect(newKeyframe.easing).toBe('linear');
    });

    it('should remove keyframe', async () => {
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      const initialKeyframeCount = wrapper.vm.animation.keyframes.length;
      
      await wrapper.vm.removeKeyframe('keyframe-1');
      
      expect(wrapper.vm.animation.keyframes).toHaveLength(initialKeyframeCount - 1);
      expect(wrapper.vm.animation.keyframes.find(k => k.id === 'keyframe-1')).toBeUndefined();
    });

    it('should duplicate keyframe', async () => {
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      const originalKeyframe = wrapper.vm.animation.keyframes[0];
      const initialKeyframeCount = wrapper.vm.animation.keyframes.length;
      
      await wrapper.vm.duplicateKeyframe('keyframe-1');
      
      expect(wrapper.vm.animation.keyframes).toHaveLength(initialKeyframeCount + 1);
      
      const duplicatedKeyframe = wrapper.vm.animation.keyframes[initialKeyframeCount];
      expect(duplicatedKeyframe.properties).toEqual(originalKeyframe.properties);
      expect(duplicatedKeyframe.time).toBe(originalKeyframe.time + 0.1);
    });

    it('should select keyframe', async () => {
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      const keyframe = wrapper.vm.animation.keyframes[0];
      
      await wrapper.vm.selectKeyframe(keyframe);
      
      expect(wrapper.vm.selectedKeyframe).toEqual(keyframe);
    });

    it('should update keyframe time', async () => {
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      const keyframe = wrapper.vm.animation.keyframes[0];
      
      await wrapper.vm.updateKeyframeTime(keyframe, 0.5);
      
      expect(keyframe.time).toBe(0.5);
    });

    it('should update keyframe easing', async () => {
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      const keyframe = wrapper.vm.animation.keyframes[0];
      
      await wrapper.vm.updateKeyframeEasing(keyframe, 'ease-in');
      
      expect(keyframe.easing).toBe('ease-in');
    });
  });

  describe('Keyframe Properties', () => {
    it('should update keyframe position', async () => {
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      const keyframe = wrapper.vm.animation.keyframes[0];
      
      await wrapper.vm.updateKeyframePosition(keyframe, { x: 5, y: 3, z: 2 });
      
      expect(keyframe.properties.position).toEqual({ x: 5, y: 3, z: 2 });
    });

    it('should update keyframe rotation', async () => {
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      const keyframe = wrapper.vm.animation.keyframes[0];
      
      await wrapper.vm.updateKeyframeRotation(keyframe, { x: 45, y: 90, z: 0 });
      
      expect(keyframe.properties.rotation).toEqual({ x: 45, y: 90, z: 0 });
    });

    it('should update keyframe scale', async () => {
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      const keyframe = wrapper.vm.animation.keyframes[0];
      
      await wrapper.vm.updateKeyframeScale(keyframe, { x: 1.5, y: 1.2, z: 1.0 });
      
      expect(keyframe.properties.scale).toEqual({ x: 1.5, y: 1.2, z: 1.0 });
    });

    it('should interpolate between keyframes', () => {
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      const startKeyframe = wrapper.vm.animation.keyframes[0];
      const endKeyframe = wrapper.vm.animation.keyframes[1];
      
      const interpolated = wrapper.vm.interpolateKeyframes(startKeyframe, endKeyframe, 0.5);
      
      expect(interpolated.properties.rotation.y).toBe(45); // Halfway between 0 and 90
    });
  });

  describe('Timeline Interaction', () => {
    it('should update playhead position', async () => {
      await wrapper.vm.updatePlayhead(1.5);
      
      expect(wrapper.vm.currentTime).toBe(1.5);
    });

    it('should snap to keyframes when enabled', async () => {
      wrapper.vm.snapToKeyframes = true;
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      
      await wrapper.vm.updatePlayhead(1.9); // Close to keyframe at 2.0
      
      expect(wrapper.vm.currentTime).toBe(2.0);
    });

    it('should handle timeline zoom', async () => {
      await wrapper.vm.setTimelineZoom(2.0);
      
      expect(wrapper.vm.timelineZoom).toBe(2.0);
    });

    it('should handle timeline scroll', async () => {
      await wrapper.vm.setTimelineScroll(0.5);
      
      expect(wrapper.vm.timelineScroll).toBe(0.5);
    });
  });

  describe('Animation Playback', () => {
    it('should play animation', async () => {
      await wrapper.vm.playAnimation();
      
      expect(wrapper.vm.isPlaying).toBe(true);
      expect(wrapper.vm.playbackStartTime).toBeDefined();
    });

    it('should pause animation', async () => {
      wrapper.vm.isPlaying = true;
      
      await wrapper.vm.pauseAnimation();
      
      expect(wrapper.vm.isPlaying).toBe(false);
    });

    it('should stop animation', async () => {
      wrapper.vm.isPlaying = true;
      wrapper.vm.currentTime = 1.5;
      
      await wrapper.vm.stopAnimation();
      
      expect(wrapper.vm.isPlaying).toBe(false);
      expect(wrapper.vm.currentTime).toBe(0);
    });

    it('should seek to specific time', async () => {
      await wrapper.vm.seekTo(1.2);
      
      expect(wrapper.vm.currentTime).toBe(1.2);
    });

    it('should handle playback speed change', async () => {
      await wrapper.vm.setPlaybackSpeed(0.5);
      
      expect(wrapper.vm.playbackSpeed).toBe(0.5);
    });
  });

  describe('Animation Triggers', () => {
    it('should add new trigger', async () => {
      wrapper.vm.animation.triggers = [...mockAnimation.triggers];
      const initialTriggerCount = wrapper.vm.animation.triggers.length;
      
      await wrapper.vm.addTrigger('hover', 'button-1');
      
      expect(wrapper.vm.animation.triggers).toHaveLength(initialTriggerCount + 1);
      
      const newTrigger = wrapper.vm.animation.triggers[initialTriggerCount];
      expect(newTrigger.type).toBe('hover');
      expect(newTrigger.target).toBe('button-1');
    });

    it('should remove trigger', async () => {
      wrapper.vm.animation.triggers = [...mockAnimation.triggers];
      const initialTriggerCount = wrapper.vm.animation.triggers.length;
      
      await wrapper.vm.removeTrigger('trigger-1');
      
      expect(wrapper.vm.animation.triggers).toHaveLength(initialTriggerCount - 1);
      expect(wrapper.vm.animation.triggers.find(t => t.id === 'trigger-1')).toBeUndefined();
    });

    it('should update trigger properties', async () => {
      wrapper.vm.animation.triggers = [...mockAnimation.triggers];
      const trigger = wrapper.vm.animation.triggers[0];
      
      await wrapper.vm.updateTrigger(trigger, {
        type: 'proximity',
        target: 'sensor-1',
        delay: 0.5
      });
      
      expect(trigger.type).toBe('proximity');
      expect(trigger.target).toBe('sensor-1');
      expect(trigger.delay).toBe(0.5);
    });
  });

  describe('Animation Curves and Easing', () => {
    it('should update easing curve', async () => {
      wrapper.vm.animation.keyframes = [...mockAnimation.keyframes];
      const keyframe = wrapper.vm.animation.keyframes[0];
      
      await wrapper.vm.updateEasingCurve(keyframe, [0.25, 0.1, 0.25, 1.0]);
      
      expect(keyframe.custom_easing).toEqual([0.25, 0.1, 0.25, 1.0]);
    });

    it('should preview easing curve', () => {
      const curvePoints = wrapper.vm.generateEasingCurve('ease-in-out', 100);
      
      expect(curvePoints).toHaveLength(100);
      expect(curvePoints[0]).toBe(0);
      expect(curvePoints[99]).toBe(1);
    });
  });

  describe('Save and Reset Functionality', () => {
    it('should save animation when save button is clicked', async () => {
      wrapper.vm.animation = { ...mockAnimation, name: 'Updated Animation' };
      
      await wrapper.vm.saveAnimation();
      
      expect(mockApi.patch).toHaveBeenCalledWith(
        '/items/animations/animation-1',
        expect.objectContaining({
          name: 'Updated Animation'
        })
      );
    });

    it('should create new animation when no ID exists', async () => {
      wrapper.vm.animation = { ...mockAnimation, id: null };
      
      await wrapper.vm.saveAnimation();
      
      expect(mockApi.post).toHaveBeenCalledWith(
        '/items/animations',
        expect.objectContaining({
          vendor_id: 'vendor-1'
        })
      );
    });

    it('should reset animation to original state', async () => {
      wrapper.vm.originalAnimation = { ...mockAnimation };
      wrapper.vm.animation = { ...mockAnimation, name: 'Changed Name' };
      
      await wrapper.vm.resetAnimation();
      
      expect(wrapper.vm.animation.name).toBe(mockAnimation.name);
      expect(wrapper.vm.hasChanges).toBe(false);
    });

    it('should validate animation before saving', async () => {
      wrapper.vm.animation = { ...mockAnimation, name: '', keyframes: [] };
      
      await wrapper.vm.saveAnimation();
      
      // Should not call API with invalid data
      expect(mockApi.patch).not.toHaveBeenCalled();
      expect(mockApi.post).not.toHaveBeenCalled();
    });
  });

  describe('Animation Import/Export', () => {
    it('should export animation data', async () => {
      wrapper.vm.animation = { ...mockAnimation };
      
      const exportedData = await wrapper.vm.exportAnimation();
      
      expect(exportedData).toEqual(expect.objectContaining({
        name: mockAnimation.name,
        keyframes: mockAnimation.keyframes,
        triggers: mockAnimation.triggers
      }));
    });

    it('should import animation data', async () => {
      const importData = {
        name: 'Imported Animation',
        duration: 3.0,
        keyframes: [
          {
            time: 0.0,
            properties: { position: { x: 0, y: 0, z: 0 } },
            easing: 'linear'
          }
        ]
      };
      
      await wrapper.vm.importAnimation(importData);
      
      expect(wrapper.vm.animation.name).toBe('Imported Animation');
      expect(wrapper.vm.animation.duration).toBe(3.0);
      expect(wrapper.vm.animation.keyframes).toHaveLength(1);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors during save', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockApi.patch.mockRejectedValueOnce(new Error('Save failed'));
      
      await wrapper.vm.saveAnimation();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should handle API errors during load', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockApi.get.mockRejectedValueOnce(new Error('Load failed'));
      
      await wrapper.vm.loadAnimations();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });
});
