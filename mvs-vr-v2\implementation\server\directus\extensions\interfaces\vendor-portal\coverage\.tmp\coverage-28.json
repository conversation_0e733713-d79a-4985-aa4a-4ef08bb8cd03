{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/VirtualListRenderer.vitest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29816, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29816, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 551, "endOffset": 6678, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 653, "endOffset": 735, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 773, "endOffset": 860, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 899, "endOffset": 1857, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 975, "endOffset": 1058, "count": 300}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1173, "endOffset": 1490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1295, "endOffset": 1482, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1933, "endOffset": 2681, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2756, "endOffset": 3558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3651, "endOffset": 4709, "count": 1}], "isBlockCoverage": true}, {"functionName": "renderer.loadMore", "ranges": [{"startOffset": 3777, "endOffset": 3839, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4777, "endOffset": 5210, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4833, "endOffset": 4924, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5277, "endOffset": 6133, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6198, "endOffset": 6674, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/utils/PerformanceOptimizer.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 126348, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 126348, "count": 1}], "isBlockCoverage": true}, {"functionName": "PerformanceOptimizer", "ranges": [{"startOffset": 873, "endOffset": 1429, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupAutoCleanup", "ranges": [{"startOffset": 1506, "endOffset": 1673, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupExpiredItems", "ranges": [{"startOffset": 1731, "endOffset": 2150, "count": 0}], "isBlockCoverage": false}, {"functionName": "approximateSize", "ranges": [{"startOffset": 2318, "endOffset": 2973, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3112, "endOffset": 3631, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 3937, "endOffset": 5217, "count": 0}], "isBlockCoverage": false}, {"functionName": "evictByMemory", "ranges": [{"startOffset": 5348, "endOffset": 6333, "count": 0}], "isBlockCoverage": false}, {"functionName": "evictLeastRecentlyUsed", "ranges": [{"startOffset": 6403, "endOffset": 6774, "count": 0}], "isBlockCoverage": false}, {"functionName": "findOldestKey", "ranges": [{"startOffset": 6874, "endOffset": 7153, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 7190, "endOffset": 7257, "count": 0}], "isBlockCoverage": false}, {"functionName": "dispose", "ranges": [{"startOffset": 7299, "endOffset": 7448, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStats", "ranges": [{"startOffset": 7532, "endOffset": 7952, "count": 0}], "isBlockCoverage": false}, {"functionName": "VirtualList<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 9004, "endOffset": 10212, "count": 6}, {"startOffset": 9314, "endOffset": 9322, "count": 0}, {"startOffset": 9371, "endOffset": 9378, "count": 0}, {"startOffset": 9427, "endOffset": 9433, "count": 0}, {"startOffset": 9472, "endOffset": 9477, "count": 0}, {"startOffset": 9578, "endOffset": 9585, "count": 0}, {"startOffset": 9905, "endOffset": 9909, "count": 0}, {"startOffset": 10036, "endOffset": 10068, "count": 0}], "isBlockCoverage": true}, {"functionName": "initWorker", "ranges": [{"startOffset": 10292, "endOffset": 11528, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleWorkerData", "ranges": [{"startOffset": 11693, "endOffset": 12266, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefetchNextPage", "ranges": [{"startOffset": 12332, "endOffset": 13073, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateScroll", "ranges": [{"startOffset": 13216, "endOffset": 13811, "count": 1}, {"startOffset": 13584, "endOffset": 13801, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadMore", "ranges": [{"startOffset": 13918, "endOffset": 16335, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVisibleItems", "ranges": [{"startOffset": 16469, "endOffset": 18731, "count": 3}, {"startOffset": 16662, "endOffset": 16731, "count": 0}, {"startOffset": 17937, "endOffset": 18014, "count": 0}, {"startOffset": 18285, "endOffset": 18301, "count": 0}, {"startOffset": 18654, "endOffset": 18669, "count": 0}, {"startOffset": 18692, "endOffset": 18701, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17153, "endOffset": 17371, "count": 60}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17961, "endOffset": 17986, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateItems", "ranges": [{"startOffset": 19036, "endOffset": 19294, "count": 1}], "isBlockCoverage": true}, {"functionName": "getMetrics", "ranges": [{"startOffset": 19384, "endOffset": 20314, "count": 1}, {"startOffset": 19502, "endOffset": 19579, "count": 0}, {"startOffset": 20001, "endOffset": 20016, "count": 0}, {"startOffset": 20057, "endOffset": 20066, "count": 0}, {"startOffset": 20123, "endOffset": 20139, "count": 0}, {"startOffset": 20257, "endOffset": 20263, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19526, "endOffset": 19551, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetMetrics", "ranges": [{"startOffset": 20361, "endOffset": 20493, "count": 1}], "isBlockCoverage": true}, {"functionName": "dispose", "ranges": [{"startOffset": 20533, "endOffset": 20783, "count": 0}], "isBlockCoverage": false}, {"functionName": "debounce", "ranges": [{"startOffset": 21001, "endOffset": 21257, "count": 0}], "isBlockCoverage": false}, {"functionName": "throttle", "ranges": [{"startOffset": 21470, "endOffset": 21725, "count": 0}], "isBlockCoverage": false}, {"functionName": "measureExecutionTime", "ranges": [{"startOffset": 21935, "endOffset": 22144, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22255, "endOffset": 22291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22402, "endOffset": 22437, "count": 6}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 22537, "endOffset": 22561, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22661, "endOffset": 22685, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22797, "endOffset": 22833, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/shared/utils/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11411, "count": 1}], "isBlockCoverage": true}, {"functionName": "setLogLevel", "ranges": [{"startOffset": 596, "endOffset": 762, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLogLevel", "ranges": [{"startOffset": 844, "endOffset": 896, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 1021, "endOffset": 1188, "count": 0}], "isBlockCoverage": false}, {"functionName": "info", "ranges": [{"startOffset": 1313, "endOffset": 1476, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1603, "endOffset": 1766, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1892, "endOffset": 2059, "count": 0}], "isBlockCoverage": false}]}]}