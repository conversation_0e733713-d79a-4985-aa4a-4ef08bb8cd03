/**
 * MaterialTextureEditor Component Unit Tests
 * 
 * Comprehensive tests for the MaterialTextureEditor component
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import MaterialTextureEditor from '../../../directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/MaterialTextureEditor.vue';

// Mock API client
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn()
};

// Mock data
const mockMaterials = [
  {
    id: 'material-1',
    name: 'Wood Material',
    vendor_id: 'vendor-1',
    type: 'Standard',
    category: 'wood',
    properties: {
      roughness: 0.8,
      metallic: 0.0,
      specular: 0.5,
      emission: 0.0
    },
    textures: {
      diffuse: '/textures/wood_diffuse.jpg',
      normal: '/textures/wood_normal.jpg',
      roughness: '/textures/wood_roughness.jpg'
    }
  },
  {
    id: 'material-2',
    name: 'Metal Material',
    vendor_id: 'vendor-1',
    type: 'PBR',
    category: 'metal',
    properties: {
      roughness: 0.2,
      metallic: 1.0,
      specular: 0.9,
      emission: 0.0
    },
    textures: {
      diffuse: '/textures/metal_diffuse.jpg',
      normal: '/textures/metal_normal.jpg',
      metallic: '/textures/metal_metallic.jpg'
    }
  }
];

const mockTextureTypes = [
  { id: 'diffuse', name: 'Diffuse', description: 'Base color texture' },
  { id: 'normal', name: 'Normal', description: 'Surface detail texture' },
  { id: 'roughness', name: 'Roughness', description: 'Surface roughness texture' },
  { id: 'metallic', name: 'Metallic', description: 'Metallic properties texture' },
  { id: 'emission', name: 'Emission', description: 'Emissive texture' },
  { id: 'occlusion', name: 'Occlusion', description: 'Ambient occlusion texture' }
];

const mockCategories = [
  { id: 'wood', name: 'Wood' },
  { id: 'metal', name: 'Metal' },
  { id: 'fabric', name: 'Fabric' },
  { id: 'glass', name: 'Glass' }
];

describe('MaterialTextureEditor Component', () => {
  let wrapper: VueWrapper<any>;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup default API responses
    mockApi.get.mockImplementation((url: string) => {
      if (url.includes('/items/materials/')) {
        return Promise.resolve({ data: { data: mockMaterials[0] } });
      }
      if (url.includes('/items/materials')) {
        return Promise.resolve({ data: { data: mockMaterials } });
      }
      if (url.includes('/items/material_categories')) {
        return Promise.resolve({ data: { data: mockCategories } });
      }
      return Promise.resolve({ data: { data: [] } });
    });

    mockApi.post.mockResolvedValue({ data: { data: { id: 'new-material-id' } } });
    mockApi.patch.mockResolvedValue({ data: { data: mockMaterials[0] } });

    // Mount component
    wrapper = mount(MaterialTextureEditor, {
      props: {
        vendorId: 'vendor-1',
        materialId: 'material-1'
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('Component Rendering', () => {
    it('should render the component', () => {
      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('.material-texture-editor').exists()).toBe(true);
    });

    it('should render the header with title and actions', () => {
      expect(wrapper.find('.editor-title').text()).toBe('Material & Texture Editor');
      expect(wrapper.find('.editor-subtitle').text()).toBe('Create and edit material properties');
      
      const saveButton = wrapper.find('button:contains("Save")');
      const resetButton = wrapper.find('button:contains("Reset")');
      
      expect(saveButton.exists()).toBe(true);
      expect(resetButton.exists()).toBe(true);
    });

    it('should render the material library', () => {
      expect(wrapper.find('.material-library').exists()).toBe(true);
      expect(wrapper.find('.search-box').exists()).toBe(true);
      expect(wrapper.find('.category-filter').exists()).toBe(true);
    });

    it('should render the material properties panel', () => {
      expect(wrapper.find('.material-properties').exists()).toBe(true);
      expect(wrapper.find('.property-controls').exists()).toBe(true);
    });

    it('should render the texture slots panel', () => {
      expect(wrapper.find('.texture-slots').exists()).toBe(true);
    });

    it('should render the preview panel', () => {
      expect(wrapper.find('.material-preview').exists()).toBe(true);
      expect(wrapper.find('.preview-sphere').exists()).toBe(true);
    });
  });

  describe('Props Validation', () => {
    it('should require vendorId prop', () => {
      expect(MaterialTextureEditor.props.vendorId.required).toBe(true);
      expect(MaterialTextureEditor.props.vendorId.type).toBe(String);
    });

    it('should have optional materialId prop', () => {
      expect(MaterialTextureEditor.props.materialId.required).toBeFalsy();
      expect(MaterialTextureEditor.props.materialId.type).toBe(String);
      expect(MaterialTextureEditor.props.materialId.default).toBe(null);
    });
  });

  describe('Data Loading', () => {
    it('should load materials on mount', async () => {
      await wrapper.vm.$nextTick();
      
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/materials')
      );
    });

    it('should load specific material when materialId is provided', async () => {
      await wrapper.vm.$nextTick();
      
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/materials/material-1')
      );
    });

    it('should initialize with default material when no materialId', async () => {
      const newWrapper = mount(MaterialTextureEditor, {
        props: {
          vendorId: 'vendor-1'
        },
        global: {
          mocks: {
            $api: mockApi
          }
        }
      });

      expect(newWrapper.vm.material.name).toBe('New Material');
      expect(newWrapper.vm.material.type).toBe('Standard');
      
      newWrapper.unmount();
    });
  });

  describe('Material Selection and Filtering', () => {
    it('should filter materials by search query', async () => {
      wrapper.vm.materials = mockMaterials;
      wrapper.vm.searchQuery = 'Wood';
      
      await wrapper.vm.filterMaterials();
      
      expect(wrapper.vm.filteredMaterials).toHaveLength(1);
      expect(wrapper.vm.filteredMaterials[0].name).toBe('Wood Material');
    });

    it('should filter materials by category', async () => {
      wrapper.vm.materials = mockMaterials;
      wrapper.vm.selectedCategory = 'wood';
      
      await wrapper.vm.filterMaterials();
      
      const woodMaterials = wrapper.vm.filteredMaterials.filter(
        (m: any) => m.category === 'wood'
      );
      expect(woodMaterials).toHaveLength(1);
    });

    it('should select material when clicked', async () => {
      const material = mockMaterials[0];
      
      await wrapper.vm.selectMaterial(material);
      
      expect(wrapper.vm.selectedMaterial).toEqual(material);
      expect(wrapper.vm.material).toEqual(material);
    });
  });

  describe('Material Properties', () => {
    it('should update material name', async () => {
      const nameInput = wrapper.find('input[placeholder="Material name"]');
      await nameInput.setValue('Updated Material Name');
      
      expect(wrapper.vm.material.name).toBe('Updated Material Name');
    });

    it('should update material type', async () => {
      const typeSelect = wrapper.find('select');
      await typeSelect.setValue('PBR');
      
      expect(wrapper.vm.material.type).toBe('PBR');
    });

    it('should update roughness property', async () => {
      await wrapper.vm.updateProperty('roughness', 0.5);
      
      expect(wrapper.vm.material.properties.roughness).toBe(0.5);
    });

    it('should update metallic property', async () => {
      await wrapper.vm.updateProperty('metallic', 0.8);
      
      expect(wrapper.vm.material.properties.metallic).toBe(0.8);
    });

    it('should update specular property', async () => {
      await wrapper.vm.updateProperty('specular', 0.7);
      
      expect(wrapper.vm.material.properties.specular).toBe(0.7);
    });

    it('should update emission property', async () => {
      await wrapper.vm.updateProperty('emission', 0.3);
      
      expect(wrapper.vm.material.properties.emission).toBe(0.3);
    });

    it('should validate property values within range', async () => {
      await wrapper.vm.updateProperty('roughness', 1.5); // Should clamp to 1.0
      expect(wrapper.vm.material.properties.roughness).toBe(1.0);
      
      await wrapper.vm.updateProperty('metallic', -0.5); // Should clamp to 0.0
      expect(wrapper.vm.material.properties.metallic).toBe(0.0);
    });
  });

  describe('Texture Management', () => {
    it('should upload texture file', async () => {
      const file = new File(['texture data'], 'texture.jpg', { type: 'image/jpeg' });
      const mockFormData = new FormData();
      mockFormData.append('file', file);
      
      mockApi.post.mockResolvedValueOnce({ 
        data: { data: { id: 'file-id', filename_download: 'texture.jpg' } } 
      });
      
      await wrapper.vm.uploadTexture('diffuse', file);
      
      expect(mockApi.post).toHaveBeenCalledWith(
        '/files',
        expect.any(FormData)
      );
      expect(wrapper.vm.material.textures.diffuse).toBe('texture.jpg');
    });

    it('should remove texture from slot', async () => {
      wrapper.vm.material.textures = { ...mockMaterials[0].textures };
      
      await wrapper.vm.removeTexture('diffuse');
      
      expect(wrapper.vm.material.textures.diffuse).toBe(null);
    });

    it('should validate texture file type', async () => {
      const invalidFile = new File(['data'], 'texture.txt', { type: 'text/plain' });
      
      const result = await wrapper.vm.validateTextureFile(invalidFile);
      
      expect(result).toBe(false);
    });

    it('should validate texture file size', async () => {
      const largeFile = new File(['x'.repeat(10 * 1024 * 1024)], 'large.jpg', { 
        type: 'image/jpeg' 
      });
      
      const result = await wrapper.vm.validateTextureFile(largeFile);
      
      expect(result).toBe(false);
    });

    it('should accept valid texture files', async () => {
      const validFile = new File(['texture data'], 'texture.jpg', { 
        type: 'image/jpeg' 
      });
      
      const result = await wrapper.vm.validateTextureFile(validFile);
      
      expect(result).toBe(true);
    });
  });

  describe('Material Preview', () => {
    it('should update preview when properties change', async () => {
      const updatePreviewSpy = vi.spyOn(wrapper.vm, 'updatePreview');
      
      await wrapper.vm.updateProperty('roughness', 0.5);
      
      expect(updatePreviewSpy).toHaveBeenCalled();
    });

    it('should update preview when textures change', async () => {
      const updatePreviewSpy = vi.spyOn(wrapper.vm, 'updatePreview');
      
      await wrapper.vm.removeTexture('diffuse');
      
      expect(updatePreviewSpy).toHaveBeenCalled();
    });

    it('should change preview geometry', async () => {
      await wrapper.vm.setPreviewGeometry('cube');
      
      expect(wrapper.vm.previewGeometry).toBe('cube');
    });

    it('should change preview environment', async () => {
      await wrapper.vm.setPreviewEnvironment('studio');
      
      expect(wrapper.vm.previewEnvironment).toBe('studio');
    });
  });

  describe('Material Presets', () => {
    it('should apply material preset', async () => {
      const woodPreset = {
        name: 'Oak Wood',
        properties: {
          roughness: 0.9,
          metallic: 0.0,
          specular: 0.4,
          emission: 0.0
        },
        textures: {
          diffuse: '/presets/oak_diffuse.jpg',
          normal: '/presets/oak_normal.jpg'
        }
      };
      
      await wrapper.vm.applyPreset(woodPreset);
      
      expect(wrapper.vm.material.properties.roughness).toBe(0.9);
      expect(wrapper.vm.material.textures.diffuse).toBe('/presets/oak_diffuse.jpg');
    });

    it('should save material as preset', async () => {
      wrapper.vm.material = { ...mockMaterials[0] };
      
      await wrapper.vm.saveAsPreset('Custom Wood');
      
      expect(mockApi.post).toHaveBeenCalledWith(
        '/items/material_presets',
        expect.objectContaining({
          name: 'Custom Wood',
          vendor_id: 'vendor-1'
        })
      );
    });
  });

  describe('Save and Reset Functionality', () => {
    it('should save material when save button is clicked', async () => {
      wrapper.vm.material = { ...mockMaterials[0], name: 'Updated Material' };
      
      await wrapper.vm.saveMaterial();
      
      expect(mockApi.patch).toHaveBeenCalledWith(
        '/items/materials/material-1',
        expect.objectContaining({
          name: 'Updated Material'
        })
      );
    });

    it('should create new material when no ID exists', async () => {
      wrapper.vm.material = { ...mockMaterials[0], id: null };
      
      await wrapper.vm.saveMaterial();
      
      expect(mockApi.post).toHaveBeenCalledWith(
        '/items/materials',
        expect.objectContaining({
          vendor_id: 'vendor-1'
        })
      );
    });

    it('should reset material to original state', async () => {
      wrapper.vm.originalMaterial = { ...mockMaterials[0] };
      wrapper.vm.material = { ...mockMaterials[0], name: 'Changed Name' };
      
      await wrapper.vm.resetMaterial();
      
      expect(wrapper.vm.material.name).toBe(mockMaterials[0].name);
      expect(wrapper.vm.hasChanges).toBe(false);
    });

    it('should validate material before saving', async () => {
      wrapper.vm.material = { ...mockMaterials[0], name: '' };
      
      await wrapper.vm.saveMaterial();
      
      // Should not call API with invalid data
      expect(mockApi.patch).not.toHaveBeenCalled();
      expect(mockApi.post).not.toHaveBeenCalled();
    });
  });

  describe('Texture Mapping', () => {
    it('should update texture UV mapping', async () => {
      await wrapper.vm.updateTextureMapping('diffuse', {
        offsetU: 0.1,
        offsetV: 0.2,
        scaleU: 2.0,
        scaleV: 1.5,
        rotation: 45
      });
      
      expect(wrapper.vm.material.textureMapping.diffuse).toEqual({
        offsetU: 0.1,
        offsetV: 0.2,
        scaleU: 2.0,
        scaleV: 1.5,
        rotation: 45
      });
    });

    it('should reset texture mapping to defaults', async () => {
      await wrapper.vm.resetTextureMapping('diffuse');
      
      expect(wrapper.vm.material.textureMapping.diffuse).toEqual({
        offsetU: 0.0,
        offsetV: 0.0,
        scaleU: 1.0,
        scaleV: 1.0,
        rotation: 0
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors during save', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockApi.patch.mockRejectedValueOnce(new Error('Save failed'));
      
      await wrapper.vm.saveMaterial();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should handle texture upload errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockApi.post.mockRejectedValueOnce(new Error('Upload failed'));
      
      const file = new File(['texture data'], 'texture.jpg', { type: 'image/jpeg' });
      await wrapper.vm.uploadTexture('diffuse', file);
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should handle API errors during load', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockApi.get.mockRejectedValueOnce(new Error('Load failed'));
      
      await wrapper.vm.loadMaterials();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });
});
