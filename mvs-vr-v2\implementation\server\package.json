{"name": "mvs-vr-server", "version": "1.0.0", "type": "module", "scripts": {"test": "vitest", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:visual-editors": "vitest run tests/unit/visual-editors", "test:report": "vitest run --coverage && node tests/generate-test-report.js"}, "dependencies": {"@testing-library/dom": "^9.3.1", "dotenv": "^16.0.3", "expect": "^29.5.0", "vitest": "^1.0.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "@supabase/supabase-js": "^2.38.0", "express-rate-limit": "^7.1.5", "ioredis": "^5.3.2", "zod": "^3.22.4", "pino": "^8.16.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@types/node": "^20.2.5", "@types/testing-library__jest-dom": "^5.14.6", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "@vitest/coverage-v8": "^1.6.1", "@vue/test-utils": "^2.4.6", "eslint": "^8.41.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest-dom": "^4.0.3", "eslint-plugin-testing-library": "^5.11.0", "eslint-plugin-vitest": "^0.2.2", "jsdom": "^26.1.0", "typescript": "^5.0.4", "vite": "^4.3.9", "vue": "^3.5.14", "chai": "^4.3.10", "sinon": "^17.0.1", "supertest": "^6.3.3", "@playwright/test": "^1.40.1", "@google-cloud/storage": "^7.7.0", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/sinon": "^17.0.2", "@types/supertest": "^2.0.16", "happy-dom": "^12.10.3", "@vitejs/plugin-vue": "^4.5.2"}}