{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/AnimationAdvancedFeatures.spec.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 48902, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 48902, "count": 1}, {"startOffset": 630, "endOffset": 48901, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 894, "endOffset": 8688, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/AnimationEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 222042, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 222042, "count": 1}, {"startOffset": 971, "endOffset": 55611, "count": 0}, {"startOffset": 55612, "endOffset": 222041, "count": 0}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 5485, "endOffset": 7328, "count": 0}], "isBlockCoverage": false}, {"functionName": "visibleAnimations", "ranges": [{"startOffset": 7462, "endOffset": 7697, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldUseVirtualScrolling", "ranges": [{"startOffset": 7859, "endOffset": 7989, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectedAnimation", "ranges": [{"startOffset": 7996, "endOffset": 8122, "count": 0}], "isBlockCoverage": false}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 8129, "endOffset": 8279, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectedTrack", "ranges": [{"startOffset": 8286, "endOffset": 8483, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 8490, "endOffset": 8697, "count": 0}], "isBlockCoverage": false}, {"functionName": "animation1", "ranges": [{"startOffset": 8750, "endOffset": 8876, "count": 0}], "isBlockCoverage": false}, {"functionName": "animation2", "ranges": [{"startOffset": 8883, "endOffset": 9009, "count": 0}], "isBlockCoverage": false}, {"functionName": "canCreateBlend", "ranges": [{"startOffset": 9016, "endOffset": 9240, "count": 0}], "isBlockCoverage": false}, {"functionName": "timeMarkers", "ranges": [{"startOffset": 9247, "endOffset": 9712, "count": 0}], "isBlockCoverage": false}, {"functionName": "scrubberPosition", "ranges": [{"startOffset": 9719, "endOffset": 9797, "count": 0}], "isBlockCoverage": false}, {"functionName": "totalKeyframeCount", "ranges": [{"startOffset": 9804, "endOffset": 10034, "count": 0}], "isBlockCoverage": false}, {"functionName": "totalTrackCount", "ranges": [{"startOffset": 10041, "endOffset": 10210, "count": 0}], "isBlockCoverage": false}, {"functionName": "created", "ranges": [{"startOffset": 10219, "endOffset": 10614, "count": 0}], "isBlockCoverage": false}, {"functionName": "mounted", "ranges": [{"startOffset": 10619, "endOffset": 11138, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 11143, "endOffset": 12021, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadData", "ranges": [{"startOffset": 12043, "endOffset": 15089, "count": 0}], "isBlockCoverage": false}, {"functionName": "createDefaultAnimation", "ranges": [{"startOffset": 15096, "endOffset": 16188, "count": 0}], "isBlockCoverage": false}, {"functionName": "timeToPosition", "ranges": [{"startOffset": 16219, "endOffset": 16289, "count": 0}], "isBlockCoverage": false}, {"functionName": "positionToTime", "ranges": [{"startOffset": 16296, "endOffset": 16430, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateTimelineWidth", "ranges": [{"startOffset": 16437, "endOffset": 16616, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatTime", "ranges": [{"startOffset": 16623, "endOffset": 17110, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDuration", "ranges": [{"startOffset": 17117, "endOffset": 17365, "count": 0}], "isBlockCoverage": false}, {"functionName": "playAnimation", "ranges": [{"startOffset": 17397, "endOffset": 18164, "count": 0}], "isBlockCoverage": false}, {"functionName": "pauseAnimation", "ranges": [{"startOffset": 18171, "endOffset": 18355, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopAnimation", "ranges": [{"startOffset": 18362, "endOffset": 18442, "count": 0}], "isBlockCoverage": false}, {"functionName": "startScrubbing", "ranges": [{"startOffset": 18474, "endOffset": 18798, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleScrubberDrag", "ranges": [{"startOffset": 18805, "endOffset": 19144, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleScroll", "ranges": [{"startOffset": 19300, "endOffset": 20259, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupResources", "ranges": [{"startOffset": 20332, "endOffset": 20798, "count": 0}], "isBlockCoverage": false}, {"functionName": "initVirtual<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 20883, "endOffset": 22324, "count": 0}], "isBlockCoverage": false}, {"functionName": "initPerformanceMonitor", "ranges": [{"startOffset": 22385, "endOffset": 22732, "count": 0}], "isBlockCoverage": false}, {"functionName": "measureRenderPerformance", "ranges": [{"startOffset": 22789, "endOffset": 24891, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadUserInfo", "ranges": [{"startOffset": 24927, "endOffset": 25282, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveUserInfo", "ranges": [{"startOffset": 25289, "endOffset": 25556, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleCollaboration", "ranges": [{"startOffset": 25563, "endOffset": 26011, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleMouseMove", "ranges": [{"startOffset": 26018, "endOffset": 26667, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCollaborativeUpdate", "ranges": [{"startOffset": 26674, "endOffset": 27112, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCollaborationConnected", "ranges": [{"startOffset": 27119, "endOffset": 27439, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCollaborationDisconnected", "ranges": [{"startOffset": 27446, "endOffset": 27717, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCollaborationError", "ranges": [{"startOffset": 27724, "endOffset": 28034, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCollaborationToggle", "ranges": [{"startOffset": 28041, "endOffset": 28125, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleRemoteSelection", "ranges": [{"startOffset": 28132, "endOffset": 28902, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadAnimationsWithPagination", "ranges": [{"startOffset": 29125, "endOffset": 33398, "count": 0}], "isBlockCoverage": false}, {"functionName": "approximateDataSize", "ranges": [{"startOffset": 33571, "endOffset": 33882, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadAnimationsWithPagination", "ranges": [{"startOffset": 34050, "endOffset": 35062, "count": 0}], "isBlockCoverage": false}, {"functionName": "measureRenderPerformance", "ranges": [{"startOffset": 35119, "endOffset": 35557, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopScrubbing", "ranges": [{"startOffset": 35564, "endOffset": 35626, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectKeyframe", "ranges": [{"startOffset": 35670, "endOffset": 35840, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateKeyframe", "ranges": [{"startOffset": 35847, "endOffset": 36070, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateKeyframeTime", "ranges": [{"startOffset": 36077, "endOffset": 36416, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateAnimationPreview", "ranges": [{"startOffset": 36423, "endOffset": 36891, "count": 0}], "isBlockCoverage": false}, {"functionName": "addTrack", "ranges": [{"startOffset": 36898, "endOffset": 38089, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveAnimations", "ranges": [{"startOffset": 38124, "endOffset": 39039, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetAnimations", "ranges": [{"startOffset": 39046, "endOffset": 39761, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAnimation", "ranges": [{"startOffset": 39768, "endOffset": 40442, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectAnimation", "ranges": [{"startOffset": 40449, "endOffset": 40837, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteAnimation", "ranges": [{"startOffset": 40844, "endOffset": 42476, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateAnimation", "ranges": [{"startOffset": 42483, "endOffset": 42743, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateAnimationDuration", "ranges": [{"startOffset": 42750, "endOffset": 43025, "count": 0}], "isBlockCoverage": false}, {"functionName": "openBlendDialog", "ranges": [{"startOffset": 43066, "endOffset": 43580, "count": 0}], "isBlockCoverage": false}, {"functionName": "closeBlendDialog", "ranges": [{"startOffset": 43587, "endOffset": 43649, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateBlendPreview", "ranges": [{"startOffset": 43656, "endOffset": 43944, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendAnimations", "ranges": [{"startOffset": 43951, "endOffset": 44284, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendKeyframes", "ranges": [{"startOffset": 44291, "endOffset": 44489, "count": 0}], "isBlockCoverage": false}, {"functionName": "getValueAtTime", "ranges": [{"startOffset": 44496, "endOffset": 44666, "count": 0}], "isBlockCoverage": false}, {"functionName": "interpolate<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 44673, "endOffset": 44882, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendValues", "ranges": [{"startOffset": 44889, "endOffset": 45062, "count": 0}], "isBlockCoverage": false}, {"functionName": "scaleTracksByFactor", "ranges": [{"startOffset": 45069, "endOffset": 45242, "count": 0}], "isBlockCoverage": false}, {"functionName": "applyEasingPreset", "ranges": [{"startOffset": 45471, "endOffset": 45737, "count": 0}], "isBlockCoverage": false}, {"functionName": "previewEasing", "ranges": [{"startOffset": 45970, "endOffset": 46321, "count": 0}], "isBlockCoverage": false}, {"functionName": "interpolateWithCurve", "ranges": [{"startOffset": 46632, "endOffset": 46945, "count": 0}], "isBlockCoverage": false}, {"functionName": "cubicBezier", "ranges": [{"startOffset": 47299, "endOffset": 47685, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCustomCurve", "ranges": [{"startOffset": 47860, "endOffset": 48156, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEasingPath", "ranges": [{"startOffset": 48320, "endOffset": 49192, "count": 0}], "isBlockCoverage": false}, {"functionName": "startDraggingControlPoint", "ranges": [{"startOffset": 49322, "endOffset": 49918, "count": 0}], "isBlockCoverage": false}, {"functionName": "dragControlPoint", "ranges": [{"startOffset": 50011, "endOffset": 51092, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopDraggingControlPoint", "ranges": [{"startOffset": 51152, "endOffset": 51405, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetCustomCurve", "ranges": [{"startOffset": 51476, "endOffset": 51663, "count": 0}], "isBlockCoverage": false}, {"functionName": "applyCustomCurve", "ranges": [{"startOffset": 51732, "endOffset": 52062, "count": 0}], "isBlockCoverage": false}, {"functionName": "createBlendedAnimation", "ranges": [{"startOffset": 52069, "endOffset": 53330, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 53339, "endOffset": 53646, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendSettings.animation1Id", "ranges": [{"startOffset": 53664, "endOffset": 53744, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendSettings.animation2Id", "ranges": [{"startOffset": 53750, "endOffset": 53830, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendSettings.factor", "ranges": [{"startOffset": 53836, "endOffset": 53910, "count": 0}], "isBlockCoverage": false}, {"functionName": "handler", "ranges": [{"startOffset": 53936, "endOffset": 54944, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 55250, "endOffset": 55356, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 55804, "endOffset": 55894, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 56014, "endOffset": 56098, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 56147, "endOffset": 56191, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/services/AnimationService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30126, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30126, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAnimations", "ranges": [{"startOffset": 596, "endOffset": 857, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 960, "endOffset": 989, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAnimation", "ranges": [{"startOffset": 1230, "endOffset": 1490, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1592, "endOffset": 1620, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAnimation", "ranges": [{"startOffset": 1869, "endOffset": 2110, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2215, "endOffset": 2246, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateAnimation", "ranges": [{"startOffset": 2549, "endOffset": 2834, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2939, "endOffset": 2970, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteAnimation", "ranges": [{"startOffset": 3217, "endOffset": 3442, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3547, "endOffset": 3578, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveAnimations", "ranges": [{"startOffset": 3897, "endOffset": 4898, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5002, "endOffset": 5032, "count": 0}], "isBlockCoverage": false}, {"functionName": "createBlendedAnimation", "ranges": [{"startOffset": 5516, "endOffset": 6194, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6306, "endOffset": 6344, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/utils/AnimationUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 52328, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 52328, "count": 1}], "isBlockCoverage": true}, {"functionName": "interpolate", "ranges": [{"startOffset": 692, "endOffset": 1070, "count": 0}], "isBlockCoverage": false}, {"functionName": "interpolateObjects", "ranges": [{"startOffset": 1363, "endOffset": 2658, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendAnimations", "ranges": [{"startOffset": 2892, "endOffset": 4756, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendKeyframes", "ranges": [{"startOffset": 5030, "endOffset": 6049, "count": 0}], "isBlockCoverage": false}, {"functionName": "getValueAtTime", "ranges": [{"startOffset": 6286, "endOffset": 7404, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendValues", "ranges": [{"startOffset": 7624, "endOffset": 7872, "count": 0}], "isBlockCoverage": false}, {"functionName": "scaleTracksByFactor", "ranges": [{"startOffset": 8053, "endOffset": 8727, "count": 0}], "isBlockCoverage": false}, {"functionName": "scaleObject", "ranges": [{"startOffset": 8888, "endOffset": 9241, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/utils/EasingFunctions.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 47293, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 47293, "count": 1}], "isBlockCoverage": true}, {"functionName": "linear", "ranges": [{"startOffset": 527, "endOffset": 535, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInQuad", "ranges": [{"startOffset": 676, "endOffset": 688, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutQuad", "ranges": [{"startOffset": 831, "endOffset": 849, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutQuad", "ranges": [{"startOffset": 1001, "endOffset": 1052, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInCubic", "ranges": [{"startOffset": 1190, "endOffset": 1206, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutCubic", "ranges": [{"startOffset": 1346, "endOffset": 1368, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutCubic", "ranges": [{"startOffset": 1517, "endOffset": 1591, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInQuart", "ranges": [{"startOffset": 1731, "endOffset": 1751, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutQuart", "ranges": [{"startOffset": 1893, "endOffset": 1919, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutQuart", "ranges": [{"startOffset": 2070, "endOffset": 2132, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInQuint", "ranges": [{"startOffset": 2272, "endOffset": 2296, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutQuint", "ranges": [{"startOffset": 2438, "endOffset": 2468, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutQuint", "ranges": [{"startOffset": 2619, "endOffset": 2691, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInSine", "ranges": [{"startOffset": 2827, "endOffset": 2863, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutSine", "ranges": [{"startOffset": 3001, "endOffset": 3033, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutSine", "ranges": [{"startOffset": 3180, "endOffset": 3219, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInExpo", "ranges": [{"startOffset": 3362, "endOffset": 3410, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutExpo", "ranges": [{"startOffset": 3555, "endOffset": 3602, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutExpo", "ranges": [{"startOffset": 3756, "endOffset": 3957, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInCirc", "ranges": [{"startOffset": 4097, "endOffset": 4128, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutCirc", "ranges": [{"startOffset": 4270, "endOffset": 4299, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutCirc", "ranges": [{"startOffset": 4450, "endOffset": 4633, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInElastic", "ranges": [{"startOffset": 4775, "endOffset": 4900, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutElastic", "ranges": [{"startOffset": 5044, "endOffset": 5167, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutElastic", "ranges": [{"startOffset": 5320, "endOffset": 5618, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInBack", "ranges": [{"startOffset": 5754, "endOffset": 5827, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutBack", "ranges": [{"startOffset": 5965, "endOffset": 6044, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutBack", "ranges": [{"startOffset": 6191, "endOffset": 6428, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInBounce", "ranges": [{"startOffset": 6568, "endOffset": 6615, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutBounce", "ranges": [{"startOffset": 6757, "endOffset": 7070, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutBounce", "ranges": [{"startOffset": 7221, "endOffset": 7365, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/utils/PerformanceOptimizer.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 126348, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 126348, "count": 1}], "isBlockCoverage": true}, {"functionName": "PerformanceOptimizer", "ranges": [{"startOffset": 873, "endOffset": 1429, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupAutoCleanup", "ranges": [{"startOffset": 1506, "endOffset": 1673, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupExpiredItems", "ranges": [{"startOffset": 1731, "endOffset": 2150, "count": 0}], "isBlockCoverage": false}, {"functionName": "approximateSize", "ranges": [{"startOffset": 2318, "endOffset": 2973, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3112, "endOffset": 3631, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 3937, "endOffset": 5217, "count": 0}], "isBlockCoverage": false}, {"functionName": "evictByMemory", "ranges": [{"startOffset": 5348, "endOffset": 6333, "count": 0}], "isBlockCoverage": false}, {"functionName": "evictLeastRecentlyUsed", "ranges": [{"startOffset": 6403, "endOffset": 6774, "count": 0}], "isBlockCoverage": false}, {"functionName": "findOldestKey", "ranges": [{"startOffset": 6874, "endOffset": 7153, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 7190, "endOffset": 7257, "count": 0}], "isBlockCoverage": false}, {"functionName": "dispose", "ranges": [{"startOffset": 7299, "endOffset": 7448, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStats", "ranges": [{"startOffset": 7532, "endOffset": 7952, "count": 0}], "isBlockCoverage": false}, {"functionName": "VirtualList<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 9004, "endOffset": 10212, "count": 0}], "isBlockCoverage": false}, {"functionName": "initWorker", "ranges": [{"startOffset": 10292, "endOffset": 11528, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleWorkerData", "ranges": [{"startOffset": 11693, "endOffset": 12266, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefetchNextPage", "ranges": [{"startOffset": 12332, "endOffset": 13073, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateScroll", "ranges": [{"startOffset": 13216, "endOffset": 13811, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadMore", "ranges": [{"startOffset": 13918, "endOffset": 16335, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVisibleItems", "ranges": [{"startOffset": 16469, "endOffset": 18731, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateItems", "ranges": [{"startOffset": 19036, "endOffset": 19294, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMetrics", "ranges": [{"startOffset": 19384, "endOffset": 20314, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetMetrics", "ranges": [{"startOffset": 20361, "endOffset": 20493, "count": 0}], "isBlockCoverage": false}, {"functionName": "dispose", "ranges": [{"startOffset": 20533, "endOffset": 20783, "count": 0}], "isBlockCoverage": false}, {"functionName": "debounce", "ranges": [{"startOffset": 21001, "endOffset": 21257, "count": 0}], "isBlockCoverage": false}, {"functionName": "throttle", "ranges": [{"startOffset": 21470, "endOffset": 21725, "count": 0}], "isBlockCoverage": false}, {"functionName": "measureExecutionTime", "ranges": [{"startOffset": 21935, "endOffset": 22144, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22255, "endOffset": 22291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22402, "endOffset": 22437, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22537, "endOffset": 22561, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22661, "endOffset": 22685, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22797, "endOffset": 22833, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1167", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/shared/utils/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11411, "count": 1}], "isBlockCoverage": true}, {"functionName": "setLogLevel", "ranges": [{"startOffset": 596, "endOffset": 762, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLogLevel", "ranges": [{"startOffset": 844, "endOffset": 896, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 1021, "endOffset": 1188, "count": 0}], "isBlockCoverage": false}, {"functionName": "info", "ranges": [{"startOffset": 1313, "endOffset": 1476, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1603, "endOffset": 1766, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1892, "endOffset": 2059, "count": 0}], "isBlockCoverage": false}]}]}