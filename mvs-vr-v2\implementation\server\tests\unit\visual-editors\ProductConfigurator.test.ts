/**
 * ProductConfigurator Component Unit Tests
 * 
 * Comprehensive tests for the ProductConfigurator component
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import ProductConfigurator from '../../../directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ProductConfigurator.vue';

// Mock API client
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn()
};

// Mock data
const mockProducts = [
  {
    id: 'product-1',
    name: 'Test Product 1',
    vendor_id: 'vendor-1',
    thumbnail: '/test-thumbnail-1.jpg',
    category: 'furniture',
    description: 'A test product'
  },
  {
    id: 'product-2',
    name: 'Test Product 2',
    vendor_id: 'vendor-1',
    thumbnail: '/test-thumbnail-2.jpg',
    category: 'lighting',
    description: 'Another test product'
  }
];

const mockConfiguration = {
  id: 'config-1',
  product_id: 'product-1',
  option_groups: [
    {
      id: 'group-1',
      name: 'Color',
      type: 'single',
      required: true,
      options: [
        { id: 'option-1', name: 'Red', value: '#FF0000', price_modifier: 0 },
        { id: 'option-2', name: 'Blue', value: '#0000FF', price_modifier: 10 }
      ]
    },
    {
      id: 'group-2',
      name: 'Size',
      type: 'single',
      required: true,
      options: [
        { id: 'option-3', name: 'Small', value: 'S', price_modifier: -20 },
        { id: 'option-4', name: 'Large', value: 'L', price_modifier: 50 }
      ]
    }
  ]
};

const mockCategories = [
  { id: 'furniture', name: 'Furniture' },
  { id: 'lighting', name: 'Lighting' },
  { id: 'decor', name: 'Decor' }
];

describe('ProductConfigurator Component', () => {
  let wrapper: VueWrapper<any>;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup default API responses
    mockApi.get.mockImplementation((url: string) => {
      if (url.includes('/items/products/')) {
        return Promise.resolve({ data: { data: mockProducts[0] } });
      }
      if (url.includes('/items/products')) {
        return Promise.resolve({ data: { data: mockProducts } });
      }
      if (url.includes('/items/product_configurations')) {
        return Promise.resolve({ data: { data: [mockConfiguration] } });
      }
      if (url.includes('/items/categories')) {
        return Promise.resolve({ data: { data: mockCategories } });
      }
      return Promise.resolve({ data: { data: [] } });
    });

    mockApi.post.mockResolvedValue({ data: { data: { id: 'new-config-id' } } });
    mockApi.patch.mockResolvedValue({ data: { data: mockConfiguration } });

    // Mount component
    wrapper = mount(ProductConfigurator, {
      props: {
        vendorId: 'vendor-1',
        productId: 'product-1'
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('Component Rendering', () => {
    it('should render the component', () => {
      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('.product-configurator').exists()).toBe(true);
    });

    it('should render the header with title and actions', () => {
      expect(wrapper.find('.editor-title').text()).toBe('Product Configurator');
      expect(wrapper.find('.editor-subtitle').text()).toBe('Create customizable product options');
      
      const saveButton = wrapper.find('button:contains("Save")');
      const resetButton = wrapper.find('button:contains("Reset")');
      
      expect(saveButton.exists()).toBe(true);
      expect(resetButton.exists()).toBe(true);
    });

    it('should render the product selection sidebar', () => {
      expect(wrapper.find('.editor-sidebar').exists()).toBe(true);
      expect(wrapper.find('.search-box').exists()).toBe(true);
      expect(wrapper.find('.product-list').exists()).toBe(true);
    });

    it('should render the configuration area', () => {
      expect(wrapper.find('.configuration-area').exists()).toBe(true);
    });
  });

  describe('Props Validation', () => {
    it('should require vendorId prop', () => {
      expect(ProductConfigurator.props.vendorId.required).toBe(true);
      expect(ProductConfigurator.props.vendorId.type).toBe(String);
    });

    it('should have optional productId prop', () => {
      expect(ProductConfigurator.props.productId.required).toBeFalsy();
      expect(ProductConfigurator.props.productId.type).toBe(String);
      expect(ProductConfigurator.props.productId.default).toBe(null);
    });
  });

  describe('Data Loading', () => {
    it('should load products on mount', async () => {
      await wrapper.vm.$nextTick();
      
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/products')
      );
    });

    it('should load product configuration when productId is provided', async () => {
      await wrapper.vm.$nextTick();
      
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/product_configurations')
      );
    });

    it('should load specific product when productId is provided', async () => {
      await wrapper.vm.$nextTick();
      
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/products/product-1')
      );
    });
  });

  describe('Product Selection', () => {
    it('should filter products by search query', async () => {
      wrapper.vm.products = mockProducts;
      wrapper.vm.searchQuery = 'Test Product 1';
      
      await wrapper.vm.filterProducts();
      
      expect(wrapper.vm.filteredProducts).toHaveLength(1);
      expect(wrapper.vm.filteredProducts[0].name).toBe('Test Product 1');
    });

    it('should select product when clicked', async () => {
      const product = mockProducts[0];
      
      await wrapper.vm.selectProduct(product);
      
      expect(wrapper.vm.selectedProduct).toEqual(product);
      expect(wrapper.vm.selectedProductId).toBe(product.id);
    });

    it('should load product configuration when product is selected', async () => {
      const product = mockProducts[0];
      
      await wrapper.vm.selectProduct(product);
      
      expect(wrapper.vm.configuration.productId).toBe(product.id);
    });
  });

  describe('Option Group Management', () => {
    it('should add new option group', async () => {
      const initialGroupCount = wrapper.vm.configuration.optionGroups.length;
      
      await wrapper.vm.addOptionGroup();
      
      expect(wrapper.vm.configuration.optionGroups).toHaveLength(initialGroupCount + 1);
      
      const newGroup = wrapper.vm.configuration.optionGroups[initialGroupCount];
      expect(newGroup.name).toBe('New Option Group');
      expect(newGroup.type).toBe('single');
      expect(newGroup.required).toBe(false);
      expect(newGroup.options).toEqual([]);
    });

    it('should remove option group', async () => {
      wrapper.vm.configuration.optionGroups = [...mockConfiguration.option_groups];
      const initialGroupCount = wrapper.vm.configuration.optionGroups.length;
      
      await wrapper.vm.removeOptionGroup(0);
      
      expect(wrapper.vm.configuration.optionGroups).toHaveLength(initialGroupCount - 1);
    });

    it('should update option group properties', async () => {
      wrapper.vm.configuration.optionGroups = [...mockConfiguration.option_groups];
      const group = wrapper.vm.configuration.optionGroups[0];
      
      group.name = 'Updated Color';
      group.required = false;
      
      expect(group.name).toBe('Updated Color');
      expect(group.required).toBe(false);
    });
  });

  describe('Option Management', () => {
    it('should add option to group', async () => {
      wrapper.vm.configuration.optionGroups = [...mockConfiguration.option_groups];
      const group = wrapper.vm.configuration.optionGroups[0];
      const initialOptionCount = group.options.length;
      
      await wrapper.vm.addOption(group);
      
      expect(group.options).toHaveLength(initialOptionCount + 1);
      
      const newOption = group.options[initialOptionCount];
      expect(newOption.name).toBe('New Option');
      expect(newOption.value).toBe('');
      expect(newOption.price_modifier).toBe(0);
    });

    it('should remove option from group', async () => {
      wrapper.vm.configuration.optionGroups = [...mockConfiguration.option_groups];
      const group = wrapper.vm.configuration.optionGroups[0];
      const initialOptionCount = group.options.length;
      
      await wrapper.vm.removeOption(group, 0);
      
      expect(group.options).toHaveLength(initialOptionCount - 1);
    });

    it('should update option properties', async () => {
      wrapper.vm.configuration.optionGroups = [...mockConfiguration.option_groups];
      const option = wrapper.vm.configuration.optionGroups[0].options[0];
      
      option.name = 'Updated Red';
      option.value = '#FF5555';
      option.price_modifier = 5;
      
      expect(option.name).toBe('Updated Red');
      expect(option.value).toBe('#FF5555');
      expect(option.price_modifier).toBe(5);
    });
  });

  describe('Option Selection and Dependencies', () => {
    it('should select option in single-select group', async () => {
      wrapper.vm.configuration.optionGroups = [...mockConfiguration.option_groups];
      const group = wrapper.vm.configuration.optionGroups[0];
      const option = group.options[0];
      
      await wrapper.vm.selectOption(group, option);
      
      expect(wrapper.vm.selectedOptions[group.id]).toBe(option.id);
    });

    it('should toggle option in multi-select group', async () => {
      wrapper.vm.configuration.optionGroups = [...mockConfiguration.option_groups];
      const group = wrapper.vm.configuration.optionGroups[0];
      group.type = 'multiple';
      const option = group.options[0];
      
      await wrapper.vm.selectOption(group, option);
      
      expect(wrapper.vm.selectedOptions[group.id]).toContain(option.id);
      
      // Toggle off
      await wrapper.vm.selectOption(group, option);
      
      expect(wrapper.vm.selectedOptions[group.id]).not.toContain(option.id);
    });

    it('should check if option is selected', () => {
      wrapper.vm.selectedOptions = { 'group-1': 'option-1' };
      
      expect(wrapper.vm.isOptionSelected('group-1', 'option-1')).toBe(true);
      expect(wrapper.vm.isOptionSelected('group-1', 'option-2')).toBe(false);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate required option groups', () => {
      wrapper.vm.configuration.optionGroups = [...mockConfiguration.option_groups];
      wrapper.vm.selectedOptions = { 'group-1': 'option-1' }; // Missing group-2
      
      const isValid = wrapper.vm.validateConfiguration();
      
      expect(isValid).toBe(false);
    });

    it('should pass validation when all required groups have selections', () => {
      wrapper.vm.configuration.optionGroups = [...mockConfiguration.option_groups];
      wrapper.vm.selectedOptions = { 
        'group-1': 'option-1',
        'group-2': 'option-3'
      };
      
      const isValid = wrapper.vm.validateConfiguration();
      
      expect(isValid).toBe(true);
    });
  });

  describe('Price Calculation', () => {
    it('should calculate total price with modifiers', () => {
      wrapper.vm.configuration.optionGroups = [...mockConfiguration.option_groups];
      wrapper.vm.selectedOptions = { 
        'group-1': 'option-2', // +10
        'group-2': 'option-4'  // +50
      };
      
      const totalModifier = wrapper.vm.calculatePriceModifier();
      
      expect(totalModifier).toBe(60);
    });

    it('should handle negative price modifiers', () => {
      wrapper.vm.configuration.optionGroups = [...mockConfiguration.option_groups];
      wrapper.vm.selectedOptions = { 
        'group-1': 'option-1', // +0
        'group-2': 'option-3'  // -20
      };
      
      const totalModifier = wrapper.vm.calculatePriceModifier();
      
      expect(totalModifier).toBe(-20);
    });
  });

  describe('Save and Reset Functionality', () => {
    it('should save configuration when save button is clicked', async () => {
      wrapper.vm.configuration = { ...mockConfiguration };
      wrapper.vm.selectedProduct = mockProducts[0];
      
      await wrapper.vm.saveConfiguration();
      
      expect(mockApi.post).toHaveBeenCalledWith(
        '/items/product_configurations',
        expect.objectContaining({
          product_id: 'product-1',
          option_groups: expect.any(Array)
        })
      );
    });

    it('should update existing configuration', async () => {
      wrapper.vm.configuration = { ...mockConfiguration, id: 'existing-config' };
      wrapper.vm.selectedProduct = mockProducts[0];
      
      await wrapper.vm.saveConfiguration();
      
      expect(mockApi.patch).toHaveBeenCalledWith(
        '/items/product_configurations/existing-config',
        expect.objectContaining({
          option_groups: expect.any(Array)
        })
      );
    });

    it('should reset configuration to original state', async () => {
      wrapper.vm.originalConfiguration = { ...mockConfiguration };
      wrapper.vm.configuration = { 
        ...mockConfiguration, 
        option_groups: [{ name: 'Changed Group' }] 
      };
      
      await wrapper.vm.resetConfiguration();
      
      expect(wrapper.vm.configuration.option_groups).toEqual(
        mockConfiguration.option_groups
      );
      expect(wrapper.vm.hasChanges).toBe(false);
    });

    it('should validate configuration before saving', async () => {
      wrapper.vm.configuration = { ...mockConfiguration };
      wrapper.vm.selectedProduct = null; // Invalid state
      
      await wrapper.vm.saveConfiguration();
      
      // Should not call API with invalid data
      expect(mockApi.post).not.toHaveBeenCalled();
      expect(mockApi.patch).not.toHaveBeenCalled();
    });
  });

  describe('Dependency Management', () => {
    it('should add dependency between options', async () => {
      const sourceGroup = 'group-1';
      const sourceOption = 'option-1';
      const targetGroup = 'group-2';
      const targetOption = 'option-3';
      
      await wrapper.vm.addDependency(sourceGroup, sourceOption, targetGroup, targetOption);
      
      expect(wrapper.vm.dependencies).toContainEqual({
        sourceGroup,
        sourceOption,
        targetGroup,
        targetOption,
        type: 'requires'
      });
    });

    it('should check option dependencies', () => {
      wrapper.vm.dependencies = [{
        sourceGroup: 'group-1',
        sourceOption: 'option-1',
        targetGroup: 'group-2',
        targetOption: 'option-3',
        type: 'requires'
      }];
      
      wrapper.vm.selectedOptions = { 'group-1': 'option-1' };
      
      const isAvailable = wrapper.vm.isOptionAvailable('group-2', 'option-4');
      expect(isAvailable).toBe(false);
      
      const isRequired = wrapper.vm.isOptionAvailable('group-2', 'option-3');
      expect(isRequired).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors during save', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockApi.post.mockRejectedValueOnce(new Error('Save failed'));
      
      wrapper.vm.configuration = { ...mockConfiguration };
      wrapper.vm.selectedProduct = mockProducts[0];
      
      await wrapper.vm.saveConfiguration();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should handle API errors during load', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockApi.get.mockRejectedValueOnce(new Error('Load failed'));
      
      await wrapper.vm.loadProducts();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });
});
