{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/tests/WizardStep.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43432, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43432, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 691, "endOffset": 9058, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1368, "endOffset": 1785, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1823, "endOffset": 1857, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1913, "endOffset": 1987, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2064, "endOffset": 2264, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2323, "endOffset": 2512, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2590, "endOffset": 3095, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3182, "endOffset": 3360, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3439, "endOffset": 3532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3615, "endOffset": 3933, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4020, "endOffset": 4417, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4487, "endOffset": 5047, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5111, "endOffset": 5692, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5754, "endOffset": 6366, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6449, "endOffset": 7052, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7133, "endOffset": 7563, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7643, "endOffset": 8186, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8275, "endOffset": 8377, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8454, "endOffset": 8692, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8782, "endOffset": 9054, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/WizardStep.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17914, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17914, "count": 1}], "isBlockCoverage": true}, {"functionName": "default", "ranges": [{"startOffset": 1585, "endOffset": 1595, "count": 0}], "isBlockCoverage": false}, {"functionName": "default", "ranges": [{"startOffset": 1724, "endOffset": 1732, "count": 0}], "isBlockCoverage": false}, {"functionName": "default", "ranges": [{"startOffset": 2008, "endOffset": 2016, "count": 17}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 2031, "endOffset": 2172, "count": 17}], "isBlockCoverage": true}, {"functionName": "hasValidationErrors", "ranges": [{"startOffset": 2193, "endOffset": 2269, "count": 33}], "isBlockCoverage": true}, {"functionName": "handler", "ranges": [{"startOffset": 2309, "endOffset": 2477, "count": 23}, {"startOffset": 2405, "endOffset": 2429, "count": 6}, {"startOffset": 2431, "endOffset": 2469, "count": 6}], "isBlockCoverage": true}, {"functionName": "mounted", "ranges": [{"startOffset": 2533, "endOffset": 2641, "count": 17}, {"startOffset": 2607, "endOffset": 2637, "count": 0}], "isBlockCoverage": true}, {"functionName": "updateStepData", "ranges": [{"startOffset": 2661, "endOffset": 2975, "count": 1}], "isBlockCoverage": true}, {"functionName": "validate", "ranges": [{"startOffset": 2982, "endOffset": 5453, "count": 13}, {"startOffset": 3029, "endOffset": 3205, "count": 0}, {"startOffset": 3377, "endOffset": 5294, "count": 39}, {"startOffset": 3539, "endOffset": 3597, "count": 26}, {"startOffset": 3599, "endOffset": 3707, "count": 2}, {"startOffset": 3653, "endOffset": 3661, "count": 0}, {"startOffset": 3707, "endOffset": 3810, "count": 37}, {"startOffset": 3810, "endOffset": 3827, "count": 31}, {"startOffset": 3828, "endOffset": 3843, "count": 31}, {"startOffset": 3845, "endOffset": 3876, "count": 8}, {"startOffset": 3876, "endOffset": 3939, "count": 29}, {"startOffset": 3939, "endOffset": 3980, "count": 13}, {"startOffset": 3982, "endOffset": 4105, "count": 2}, {"startOffset": 4036, "endOffset": 4044, "count": 0}, {"startOffset": 4105, "endOffset": 4168, "count": 29}, {"startOffset": 4168, "endOffset": 4209, "count": 0}, {"startOffset": 4211, "endOffset": 4333, "count": 0}, {"startOffset": 4333, "endOffset": 4391, "count": 29}, {"startOffset": 4391, "endOffset": 4440, "count": 5}, {"startOffset": 4442, "endOffset": 4553, "count": 3}, {"startOffset": 4502, "endOffset": 4541, "count": 0}, {"startOffset": 4553, "endOffset": 4623, "count": 29}, {"startOffset": 4623, "endOffset": 4651, "count": 0}, {"startOffset": 4653, "endOffset": 4759, "count": 0}, {"startOffset": 4759, "endOffset": 4829, "count": 29}, {"startOffset": 4829, "endOffset": 4857, "count": 0}, {"startOffset": 4859, "endOffset": 4964, "count": 0}, {"startOffset": 4964, "endOffset": 5033, "count": 29}, {"startOffset": 5033, "endOffset": 5072, "count": 0}, {"startOffset": 5074, "endOffset": 5286, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateStep", "ranges": [{"startOffset": 5460, "endOffset": 5512, "count": 1}], "isBlockCoverage": true}, {"functionName": "resetValidation", "ranges": [{"startOffset": 5519, "endOffset": 5578, "count": 1}], "isBlockCoverage": true}, {"functionName": "toggleHelpTips", "ranges": [{"startOffset": 5585, "endOffset": 5659, "count": 0}], "isBlockCoverage": false}, {"functionName": "isFieldDisabled", "ranges": [{"startOffset": 5666, "endOffset": 5754, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 6035, "endOffset": 6141, "count": 17}, {"startOffset": 6104, "endOffset": 6139, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6583, "endOffset": 6672, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6791, "endOffset": 6874, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6923, "endOffset": 6967, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/WizardStep.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10973, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10973, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 1573, "count": 34}, {"startOffset": 278, "endOffset": 282, "count": 0}, {"startOffset": 584, "endOffset": 594, "count": 0}, {"startOffset": 597, "endOffset": 607, "count": 0}, {"startOffset": 707, "endOffset": 1091, "count": 9}, {"startOffset": 1092, "endOffset": 1102, "count": 25}, {"startOffset": 1121, "endOffset": 1147, "count": 33}, {"startOffset": 1148, "endOffset": 1557, "count": 33}, {"startOffset": 1558, "endOffset": 1568, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 938, "endOffset": 1084, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1217, "endOffset": 1552, "count": 66}], "isBlockCoverage": true}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 1598, "endOffset": 1816, "count": 66}, {"startOffset": 1678, "endOffset": 1682, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1942, "endOffset": 1964, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2071, "endOffset": 2102, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/WizardStep.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 554, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 554, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 1}, {"startOffset": 502, "endOffset": 525, "count": 0}, {"startOffset": 746, "endOffset": 781, "count": 0}, {"startOffset": 897, "endOffset": 1767, "count": 0}, {"startOffset": 1823, "endOffset": 1991, "count": 0}, {"startOffset": 2062, "endOffset": 2466, "count": 0}, {"startOffset": 2624, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "hook", "ranges": [{"startOffset": 926, "endOffset": 1642, "count": 0}], "isBlockCoverage": false}, {"functionName": "hook", "ranges": [{"startOffset": 1825, "endOffset": 1991, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithStyleInjection", "ranges": [{"startOffset": 2335, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 1}, {"startOffset": 543, "endOffset": 553, "count": 0}, {"startOffset": 597, "endOffset": 610, "count": 0}, {"startOffset": 781, "endOffset": 812, "count": 0}, {"startOffset": 904, "endOffset": 1064, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 1}, {"startOffset": 1294, "endOffset": 1304, "count": 0}, {"startOffset": 1361, "endOffset": 1412, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 1}, {"startOffset": 1843, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "options.render", "ranges": [{"startOffset": 1898, "endOffset": 2088, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2138, "endOffset": 2290, "count": 17}, {"startOffset": 2202, "endOffset": 2250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2442, "count": 17}], "isBlockCoverage": true}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 2}, {"startOffset": 2728, "endOffset": 2796, "count": 1}, {"startOffset": 2778, "endOffset": 2796, "count": 0}, {"startOffset": 2801, "endOffset": 2809, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}]}