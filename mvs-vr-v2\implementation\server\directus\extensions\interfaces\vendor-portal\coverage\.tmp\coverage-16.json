{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/tests/GuidedSetupWizard.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33115, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33115, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 351, "endOffset": 705, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 414, "endOffset": 427, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 751, "endOffset": 864, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 817, "endOffset": 830, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 907, "endOffset": 1017, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 970, "endOffset": 983, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1062, "endOffset": 1174, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1127, "endOffset": 1140, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1219, "endOffset": 1331, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1284, "endOffset": 1297, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1377, "endOffset": 1490, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1443, "endOffset": 1456, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1532, "endOffset": 1641, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1594, "endOffset": 1607, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1692, "endOffset": 1825, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3148, "endOffset": 8241, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3185, "endOffset": 3331, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3347, "endOffset": 3405, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3439, "endOffset": 3491, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3541, "endOffset": 3632, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3688, "endOffset": 3995, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4039, "endOffset": 4576, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4625, "endOffset": 5538, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5576, "endOffset": 6714, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6748, "endOffset": 7532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7569, "endOffset": 8083, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8121, "endOffset": 8237, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/GuidedSetupWizard.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28909, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28909, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 3705, "endOffset": 7390, "count": 10}], "isBlockCoverage": true}, {"functionName": "mounted", "ranges": [{"startOffset": 7397, "endOffset": 7534, "count": 10}], "isBlockCoverage": true}, {"functionName": "handleWizardComplete", "ranges": [{"startOffset": 7556, "endOffset": 8297, "count": 1}, {"startOffset": 8084, "endOffset": 8241, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleSaveProgress", "ranges": [{"startOffset": 8308, "endOffset": 8948, "count": 1}, {"startOffset": 8746, "endOffset": 8892, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleLoadProgress", "ranges": [{"startOffset": 8959, "endOffset": 9183, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleAnalyticsEvent", "ranges": [{"startOffset": 9194, "endOffset": 9328, "count": 1}], "isBlockCoverage": true}, {"functionName": "handleExit", "ranges": [{"startOffset": 9339, "endOffset": 9410, "count": 1}], "isBlockCoverage": true}, {"functionName": "loadOnboardingStatus", "ranges": [{"startOffset": 9421, "endOffset": 10292, "count": 10}, {"startOffset": 9623, "endOffset": 9646, "count": 5}, {"startOffset": 9648, "endOffset": 10062, "count": 5}, {"startOffset": 9816, "endOffset": 9838, "count": 0}, {"startOffset": 10071, "endOffset": 10236, "count": 0}], "isBlockCoverage": true}, {"functionName": "trackAnalyticsEvent", "ranges": [{"startOffset": 10303, "endOffset": 10661, "count": 1}, {"startOffset": 10508, "endOffset": 10655, "count": 0}], "isBlockCoverage": true}, {"functionName": "trackWizardCompletion", "ranges": [{"startOffset": 10672, "endOffset": 11099, "count": 1}, {"startOffset": 10944, "endOffset": 11093, "count": 0}], "isBlockCoverage": true}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 11381, "endOffset": 11487, "count": 10}, {"startOffset": 11450, "endOffset": 11485, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11941, "endOffset": 12031, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12157, "endOffset": 12241, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12290, "endOffset": 12334, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/GuidedSetupWizard.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7472, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7472, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 1322, "count": 10}, {"startOffset": 278, "endOffset": 282, "count": 0}, {"startOffset": 1304, "endOffset": 1314, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1473, "endOffset": 1495, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1602, "endOffset": 1633, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/GuidedSetupWizard.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 562, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 562, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 1}, {"startOffset": 502, "endOffset": 525, "count": 0}, {"startOffset": 746, "endOffset": 781, "count": 0}, {"startOffset": 897, "endOffset": 1767, "count": 0}, {"startOffset": 1823, "endOffset": 1991, "count": 0}, {"startOffset": 2062, "endOffset": 2466, "count": 0}, {"startOffset": 2624, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "hook", "ranges": [{"startOffset": 926, "endOffset": 1642, "count": 0}], "isBlockCoverage": false}, {"functionName": "hook", "ranges": [{"startOffset": 1825, "endOffset": 1991, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithStyleInjection", "ranges": [{"startOffset": 2335, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 1}, {"startOffset": 543, "endOffset": 553, "count": 0}, {"startOffset": 597, "endOffset": 610, "count": 0}, {"startOffset": 781, "endOffset": 812, "count": 0}, {"startOffset": 904, "endOffset": 1064, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 1}, {"startOffset": 1294, "endOffset": 1304, "count": 0}, {"startOffset": 1361, "endOffset": 1412, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 1}, {"startOffset": 1843, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "options.render", "ranges": [{"startOffset": 1898, "endOffset": 2088, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2138, "endOffset": 2290, "count": 10}, {"startOffset": 2202, "endOffset": 2250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2442, "count": 9}], "isBlockCoverage": true}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 2}, {"startOffset": 2728, "endOffset": 2796, "count": 1}, {"startOffset": 2778, "endOffset": 2796, "count": 0}, {"startOffset": 2801, "endOffset": 2809, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}]}