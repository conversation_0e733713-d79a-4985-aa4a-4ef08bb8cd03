{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/PerformanceOptimizer.vitest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27555, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 553, "endOffset": 6165, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 610, "endOffset": 879, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 917, "endOffset": 1061, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1137, "endOffset": 1693, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1759, "endOffset": 1981, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2057, "endOffset": 2249, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2313, "endOffset": 2679, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2761, "endOffset": 3185, "count": 1}, {"startOffset": 2855, "endOffset": 2991, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3257, "endOffset": 3959, "count": 1}, {"startOffset": 3320, "endOffset": 3368, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4029, "endOffset": 4608, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4677, "endOffset": 5140, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5214, "endOffset": 5563, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5641, "endOffset": 6161, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/utils/PerformanceOptimizer.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 126348, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 126348, "count": 1}], "isBlockCoverage": true}, {"functionName": "PerformanceOptimizer", "ranges": [{"startOffset": 873, "endOffset": 1429, "count": 10}, {"startOffset": 1125, "endOffset": 1144, "count": 0}, {"startOffset": 1217, "endOffset": 1223, "count": 0}], "isBlockCoverage": true}, {"functionName": "setupAutoCleanup", "ranges": [{"startOffset": 1506, "endOffset": 1673, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1609, "endOffset": 1656, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupExpiredItems", "ranges": [{"startOffset": 1731, "endOffset": 2150, "count": 1}, {"startOffset": 1887, "endOffset": 2028, "count": 2}, {"startOffset": 1918, "endOffset": 2022, "count": 1}, {"startOffset": 1953, "endOffset": 1957, "count": 0}], "isBlockCoverage": true}, {"functionName": "approximateSize", "ranges": [{"startOffset": 2318, "endOffset": 2973, "count": 25}, {"startOffset": 2390, "endOffset": 2399, "count": 0}, {"startOffset": 2461, "endOffset": 2470, "count": 0}, {"startOffset": 2498, "endOffset": 2507, "count": 0}, {"startOffset": 2559, "endOffset": 2972, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2652, "endOffset": 2701, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2814, "endOffset": 2911, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3112, "endOffset": 3631, "count": 10}, {"startOffset": 3153, "endOffset": 3200, "count": 3}, {"startOffset": 3200, "endOffset": 3308, "count": 7}, {"startOffset": 3308, "endOffset": 3457, "count": 1}, {"startOffset": 3380, "endOffset": 3384, "count": 0}, {"startOffset": 3457, "endOffset": 3580, "count": 6}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 3937, "endOffset": 5217, "count": 25}, {"startOffset": 3997, "endOffset": 4008, "count": 23}, {"startOffset": 4225, "endOffset": 4376, "count": 0}, {"startOffset": 4474, "endOffset": 4573, "count": 0}, {"startOffset": 4725, "endOffset": 4819, "count": 2}, {"startOffset": 4952, "endOffset": 4996, "count": 1}, {"startOffset": 4996, "endOffset": 5216, "count": 24}], "isBlockCoverage": true}, {"functionName": "evictByMemory", "ranges": [{"startOffset": 5348, "endOffset": 6333, "count": 2}, {"startOffset": 5403, "endOffset": 5410, "count": 0}, {"startOffset": 6071, "endOffset": 6252, "count": 4}, {"startOffset": 6110, "endOffset": 6116, "count": 0}, {"startOffset": 6240, "endOffset": 6246, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5527, "endOffset": 5683, "count": 4}, {"startOffset": 5669, "endOffset": 5673, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5697, "endOffset": 5935, "count": 2}, {"startOffset": 5820, "endOffset": 5879, "count": 0}], "isBlockCoverage": true}, {"functionName": "evictLeastRecentlyUsed", "ranges": [{"startOffset": 6403, "endOffset": 6774, "count": 1}, {"startOffset": 6582, "endOffset": 6586, "count": 0}], "isBlockCoverage": true}, {"functionName": "findOldestKey", "ranges": [{"startOffset": 6874, "endOffset": 7153, "count": 1}, {"startOffset": 7002, "endOffset": 7126, "count": 10}, {"startOffset": 7046, "endOffset": 7120, "count": 1}], "isBlockCoverage": true}, {"functionName": "clear", "ranges": [{"startOffset": 7190, "endOffset": 7257, "count": 11}], "isBlockCoverage": true}, {"functionName": "dispose", "ranges": [{"startOffset": 7299, "endOffset": 7448, "count": 11}, {"startOffset": 7341, "endOffset": 7426, "count": 10}], "isBlockCoverage": true}, {"functionName": "getStats", "ranges": [{"startOffset": 7532, "endOffset": 7952, "count": 1}, {"startOffset": 7789, "endOffset": 7793, "count": 0}], "isBlockCoverage": true}, {"functionName": "VirtualList<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 9004, "endOffset": 10212, "count": 0}], "isBlockCoverage": false}, {"functionName": "initWorker", "ranges": [{"startOffset": 10292, "endOffset": 11528, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleWorkerData", "ranges": [{"startOffset": 11693, "endOffset": 12266, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefetchNextPage", "ranges": [{"startOffset": 12332, "endOffset": 13073, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateScroll", "ranges": [{"startOffset": 13216, "endOffset": 13811, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadMore", "ranges": [{"startOffset": 13918, "endOffset": 16335, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVisibleItems", "ranges": [{"startOffset": 16469, "endOffset": 18731, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateItems", "ranges": [{"startOffset": 19036, "endOffset": 19294, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMetrics", "ranges": [{"startOffset": 19384, "endOffset": 20314, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetMetrics", "ranges": [{"startOffset": 20361, "endOffset": 20493, "count": 0}], "isBlockCoverage": false}, {"functionName": "dispose", "ranges": [{"startOffset": 20533, "endOffset": 20783, "count": 0}], "isBlockCoverage": false}, {"functionName": "debounce", "ranges": [{"startOffset": 21001, "endOffset": 21257, "count": 0}], "isBlockCoverage": false}, {"functionName": "throttle", "ranges": [{"startOffset": 21470, "endOffset": 21725, "count": 0}], "isBlockCoverage": false}, {"functionName": "measureExecutionTime", "ranges": [{"startOffset": 21935, "endOffset": 22144, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22255, "endOffset": 22291, "count": 10}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 22402, "endOffset": 22437, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22537, "endOffset": 22561, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22661, "endOffset": 22685, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22797, "endOffset": 22833, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/shared/utils/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11411, "count": 1}], "isBlockCoverage": true}, {"functionName": "setLogLevel", "ranges": [{"startOffset": 596, "endOffset": 762, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLogLevel", "ranges": [{"startOffset": 844, "endOffset": 896, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 1021, "endOffset": 1188, "count": 0}], "isBlockCoverage": false}, {"functionName": "info", "ranges": [{"startOffset": 1313, "endOffset": 1476, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1603, "endOffset": 1766, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1892, "endOffset": 2059, "count": 0}], "isBlockCoverage": false}]}]}