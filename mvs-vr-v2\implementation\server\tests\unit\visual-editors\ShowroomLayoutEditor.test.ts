/**
 * ShowroomLayoutEditor Component Unit Tests
 * 
 * Comprehensive tests for the ShowroomLayoutEditor component
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import ShowroomLayoutEditor from '../../../directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue';

// Mock API client
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn()
};

// Mock data
const mockLayout = {
  id: 'layout-1',
  name: 'Test Showroom',
  template: 'template_1',
  size: 'medium',
  items: [
    {
      id: 'item-1',
      productId: 'product-1',
      x: 100,
      y: 100,
      rotation: 0,
      scale: 1
    }
  ]
};

const mockProducts = [
  {
    id: 'product-1',
    name: 'Test Product 1',
    vendor_id: 'vendor-1',
    thumbnail: '/test-thumbnail-1.jpg',
    category: 'furniture'
  },
  {
    id: 'product-2',
    name: 'Test Product 2',
    vendor_id: 'vendor-1',
    thumbnail: '/test-thumbnail-2.jpg',
    category: 'lighting'
  }
];

const mockCategories = [
  { id: 'furniture', name: 'Furniture' },
  { id: 'lighting', name: 'Lighting' },
  { id: 'decor', name: 'Decor' }
];

const mockTemplates = [
  { id: 'template_1', name: 'Modern Showroom' },
  { id: 'template_2', name: 'Classic Gallery' },
  { id: 'template_3', name: 'Open Space' }
];

describe('ShowroomLayoutEditor Component', () => {
  let wrapper: VueWrapper<any>;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup default API responses
    mockApi.get.mockImplementation((url: string) => {
      if (url.includes('/items/showroom_layouts/')) {
        return Promise.resolve({ data: { data: mockLayout } });
      }
      if (url.includes('/items/products')) {
        return Promise.resolve({ data: { data: mockProducts } });
      }
      if (url.includes('/items/categories')) {
        return Promise.resolve({ data: { data: mockCategories } });
      }
      return Promise.resolve({ data: { data: [] } });
    });

    mockApi.post.mockResolvedValue({ data: { data: { id: 'new-layout-id' } } });
    mockApi.patch.mockResolvedValue({ data: { data: mockLayout } });

    // Mount component
    wrapper = mount(ShowroomLayoutEditor, {
      props: {
        vendorId: 'vendor-1',
        showroomId: 'layout-1'
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('Component Rendering', () => {
    it('should render the component', () => {
      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('.showroom-layout-editor').exists()).toBe(true);
    });

    it('should render the header with title and actions', () => {
      expect(wrapper.find('.editor-title').text()).toBe('Showroom Layout Editor');
      expect(wrapper.find('.editor-subtitle').text()).toBe('Design your virtual showroom layout');
      
      const saveButton = wrapper.find('button:contains("Save")');
      const resetButton = wrapper.find('button:contains("Reset")');
      
      expect(saveButton.exists()).toBe(true);
      expect(resetButton.exists()).toBe(true);
    });

    it('should render the layout configuration section', () => {
      expect(wrapper.find('.layout-config').exists()).toBe(true);
      expect(wrapper.find('input[placeholder="Showroom name"]').exists()).toBe(true);
      expect(wrapper.find('select').exists()).toBe(true); // Template selector
    });

    it('should render the product library', () => {
      expect(wrapper.find('.product-library').exists()).toBe(true);
      expect(wrapper.find('.search-box').exists()).toBe(true);
      expect(wrapper.find('.category-filter').exists()).toBe(true);
    });

    it('should render the layout canvas', () => {
      expect(wrapper.find('.layout-canvas').exists()).toBe(true);
      expect(wrapper.find('.canvas-container').exists()).toBe(true);
    });
  });

  describe('Props Validation', () => {
    it('should require vendorId prop', () => {
      expect(ShowroomLayoutEditor.props.vendorId.required).toBe(true);
      expect(ShowroomLayoutEditor.props.vendorId.type).toBe(String);
    });

    it('should have optional showroomId prop', () => {
      expect(ShowroomLayoutEditor.props.showroomId.required).toBeFalsy();
      expect(ShowroomLayoutEditor.props.showroomId.type).toBe(String);
      expect(ShowroomLayoutEditor.props.showroomId.default).toBe(null);
    });
  });

  describe('Data Loading', () => {
    it('should load layout data when showroomId is provided', async () => {
      await wrapper.vm.$nextTick();
      
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/showroom_layouts/layout-1')
      );
    });

    it('should load products and categories on mount', async () => {
      await wrapper.vm.$nextTick();
      
      expect(mockApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/items/products')
      );
    });

    it('should initialize with default layout when no showroomId', async () => {
      const newWrapper = mount(ShowroomLayoutEditor, {
        props: {
          vendorId: 'vendor-1'
        },
        global: {
          mocks: {
            $api: mockApi
          }
        }
      });

      expect(newWrapper.vm.layout.name).toBe('New Showroom');
      expect(newWrapper.vm.layout.template).toBe('template_1');
      expect(newWrapper.vm.layout.size).toBe('medium');
      
      newWrapper.unmount();
    });
  });

  describe('Layout Configuration', () => {
    it('should update layout name when input changes', async () => {
      const nameInput = wrapper.find('input[placeholder="Showroom name"]');
      await nameInput.setValue('Updated Showroom Name');
      
      expect(wrapper.vm.layout.name).toBe('Updated Showroom Name');
    });

    it('should update template when selection changes', async () => {
      const templateSelect = wrapper.find('select');
      await templateSelect.setValue('template_2');
      
      expect(wrapper.vm.layout.template).toBe('template_2');
    });

    it('should mark layout as changed when modifications are made', async () => {
      const nameInput = wrapper.find('input[placeholder="Showroom name"]');
      await nameInput.setValue('Changed Name');
      
      expect(wrapper.vm.hasChanges).toBe(true);
    });
  });

  describe('Product Search and Filtering', () => {
    it('should filter products by search query', async () => {
      wrapper.vm.products = mockProducts;
      wrapper.vm.searchQuery = 'Test Product 1';
      
      await wrapper.vm.filterProducts();
      
      expect(wrapper.vm.filteredProducts).toHaveLength(1);
      expect(wrapper.vm.filteredProducts[0].name).toBe('Test Product 1');
    });

    it('should filter products by category', async () => {
      wrapper.vm.products = mockProducts;
      wrapper.vm.selectedCategory = 'furniture';
      
      await wrapper.vm.filterProducts();
      
      const furnitureProducts = wrapper.vm.filteredProducts.filter(
        (p: any) => p.category === 'furniture'
      );
      expect(furnitureProducts).toHaveLength(1);
    });

    it('should show all products when no filters applied', async () => {
      wrapper.vm.products = mockProducts;
      wrapper.vm.searchQuery = '';
      wrapper.vm.selectedCategory = '';
      
      await wrapper.vm.filterProducts();
      
      expect(wrapper.vm.filteredProducts).toHaveLength(mockProducts.length);
    });
  });

  describe('Drag and Drop Functionality', () => {
    it('should handle drag start for products', async () => {
      const product = mockProducts[0];
      const dragEvent = new DragEvent('dragstart');
      
      await wrapper.vm.handleDragStart(dragEvent, product);
      
      expect(wrapper.vm.draggedItem).toEqual(product);
    });

    it('should handle drop on canvas', async () => {
      const product = mockProducts[0];
      wrapper.vm.draggedItem = product;
      
      const dropEvent = new DragEvent('drop');
      Object.defineProperty(dropEvent, 'offsetX', { value: 150 });
      Object.defineProperty(dropEvent, 'offsetY', { value: 200 });
      
      await wrapper.vm.handleDrop(dropEvent);
      
      expect(wrapper.vm.layout.items).toContainEqual(
        expect.objectContaining({
          productId: product.id,
          x: 150,
          y: 200
        })
      );
    });

    it('should prevent default drag over behavior', async () => {
      const dragOverEvent = new DragEvent('dragover');
      const preventDefaultSpy = vi.spyOn(dragOverEvent, 'preventDefault');
      
      await wrapper.vm.handleDragOver(dragOverEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
    });
  });

  describe('Item Management', () => {
    it('should select item when clicked', async () => {
      const item = mockLayout.items[0];
      
      await wrapper.vm.selectItem(item);
      
      expect(wrapper.vm.selectedItem).toEqual(item);
    });

    it('should delete selected item', async () => {
      wrapper.vm.layout.items = [...mockLayout.items];
      wrapper.vm.selectedItem = mockLayout.items[0];
      
      await wrapper.vm.deleteSelectedItem();
      
      expect(wrapper.vm.layout.items).toHaveLength(0);
      expect(wrapper.vm.selectedItem).toBe(null);
    });

    it('should update item position', async () => {
      wrapper.vm.layout.items = [...mockLayout.items];
      const item = wrapper.vm.layout.items[0];
      
      await wrapper.vm.updateItemPosition(item, 200, 300);
      
      expect(item.x).toBe(200);
      expect(item.y).toBe(300);
    });

    it('should update item rotation', async () => {
      wrapper.vm.layout.items = [...mockLayout.items];
      const item = wrapper.vm.layout.items[0];
      
      await wrapper.vm.updateItemRotation(item, 45);
      
      expect(item.rotation).toBe(45);
    });
  });

  describe('Save and Reset Functionality', () => {
    it('should save layout when save button is clicked', async () => {
      wrapper.vm.layout = { ...mockLayout, name: 'Updated Layout' };
      
      await wrapper.vm.saveLayout();
      
      expect(mockApi.patch).toHaveBeenCalledWith(
        '/items/showroom_layouts/layout-1',
        expect.objectContaining({
          name: 'Updated Layout'
        })
      );
    });

    it('should create new layout when no ID exists', async () => {
      wrapper.vm.layout = { ...mockLayout, id: null };
      
      await wrapper.vm.saveLayout();
      
      expect(mockApi.post).toHaveBeenCalledWith(
        '/items/showroom_layouts',
        expect.objectContaining({
          vendor_id: 'vendor-1'
        })
      );
    });

    it('should reset layout to original state', async () => {
      wrapper.vm.originalLayout = { ...mockLayout };
      wrapper.vm.layout = { ...mockLayout, name: 'Changed Name' };
      
      await wrapper.vm.resetLayout();
      
      expect(wrapper.vm.layout.name).toBe(mockLayout.name);
      expect(wrapper.vm.hasChanges).toBe(false);
    });

    it('should validate layout before saving', async () => {
      wrapper.vm.layout = { ...mockLayout, name: '' };
      
      await wrapper.vm.saveLayout();
      
      // Should not call API with invalid data
      expect(mockApi.patch).not.toHaveBeenCalled();
      expect(mockApi.post).not.toHaveBeenCalled();
    });
  });

  describe('View Controls', () => {
    it('should toggle grid visibility', async () => {
      const initialShowGrid = wrapper.vm.showGrid;
      
      await wrapper.vm.toggleGrid();
      
      expect(wrapper.vm.showGrid).toBe(!initialShowGrid);
    });

    it('should toggle snap to grid', async () => {
      const initialSnapToGrid = wrapper.vm.snapToGrid;
      
      await wrapper.vm.toggleSnapToGrid();
      
      expect(wrapper.vm.snapToGrid).toBe(!initialSnapToGrid);
    });

    it('should change zoom level', async () => {
      await wrapper.vm.setZoomLevel(1.5);
      
      expect(wrapper.vm.zoomLevel).toBe(1.5);
    });

    it('should switch view mode', async () => {
      await wrapper.vm.setViewMode('3d');
      
      expect(wrapper.vm.viewMode).toBe('3d');
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors during save', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockApi.patch.mockRejectedValueOnce(new Error('Save failed'));
      
      await wrapper.vm.saveLayout();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('should handle API errors during load', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockApi.get.mockRejectedValueOnce(new Error('Load failed'));
      
      await wrapper.vm.loadLayout();
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });
});
